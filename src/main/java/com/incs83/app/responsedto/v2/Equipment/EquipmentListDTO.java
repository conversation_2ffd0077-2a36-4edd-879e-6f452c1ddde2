package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentListDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentListData> data;

    @Override
    public ArrayList<EquipmentListData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentListData> data) {
        this.data = data;
    }

    public static class EquipmentListData {
        @ApiModelProperty(notes = "serialNumber", required = true)
        private String serialNumber;
        @ApiModelProperty(notes = "name", required = true)
        private String name;
        @ApiModelProperty(notes = "type", required = true)
        private String type;
        @ApiModelProperty(notes = "connectivityStatus", required = true)
        private String connectivityStatus;
        @ApiModelProperty(notes = "Mac Address", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "Severity Level (0 is lowest)")
        private int severity;
        @ApiModelProperty(notes = "Build version", required = true)
        private String buildVersion;
        @ApiModelProperty(notes = "Firmware version", required = true)
        private String fwVersion;
        @ApiModelProperty(notes = "Isp")
        private String isp;
        @ApiModelProperty(notes = "Diagnostics Enabled")
        private Boolean diagnosticsEnabled;

        @ApiModelProperty(notes = "Diagnostics Enabled")
        private String etlVersion;

        public String getEtlVersion() {
            return etlVersion;
        }

        public void setEtlVersion(String etlVersion) {
            this.etlVersion = etlVersion;
        }

        public Boolean getDiagnosticsEnabled() {
            return diagnosticsEnabled;
        }

        public void setDiagnosticsEnabled(Boolean diagnosticsEnabled) {
            this.diagnosticsEnabled = diagnosticsEnabled;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getConnectivityStatus() {
            return connectivityStatus;
        }

        public void setConnectivityStatus(String connectivityStatus) {
            this.connectivityStatus = connectivityStatus;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public int getSeverity() {
            return severity;
        }

        public void setSeverity(int severity) {
            this.severity = severity;
        }

        public String getBuildVersion() {
            return buildVersion;
        }

        public void setBuildVersion(String buildVersion) {
            this.buildVersion = buildVersion;
        }

        public String getFwVersion() {
            return fwVersion;
        }

        public void setFwVersion(String fwVersion) {
            this.fwVersion = fwVersion;
        }

        public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }
    }
}
