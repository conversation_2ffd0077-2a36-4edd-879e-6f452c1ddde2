package com.incs83.app.business.v3;

import com.actiontec.optim.platform.annotation.OperationHandle;
import com.actiontec.optim.platform.constant.OperationConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.annotation.Cachable;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.RPCUtilityService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.constants.misc.FsecureConstants;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.entities.Equipment;
import com.incs83.app.enums.ProfileCategories;
import com.incs83.app.responsedto.v3.*;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.service.MongoService;
import com.incs83.util.CommonUtils;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.THREAD_TO_SLEEP;
import static com.incs83.app.constants.misc.FsecureConstants.DEVICE_TRIGGER_ACTION;

@Service
public class ManageFSecureService {
    private static final Logger LOG = LogManager.getLogger(ManageFSecureService.class);

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private RPCUtilityService rpcUtilityService;

    @Auditable(operation = AuditorConstants.PROFILE_ACTION_CREATE, method = RequestMethod.POST)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object triggerActionForProfile(String equipmentIdOrSerialOrSTN, String profileId, ProfileActionRequest actionRequest, HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, Object> payload = new HashMap<>();
        if (Objects.nonNull(actionRequest.getAction()))
            payload.put("action", actionRequest.getAction());
        if (Objects.nonNull(actionRequest.getDelay()))
            payload.put("delay", actionRequest.getDelay());
        if (Objects.nonNull(actionRequest.getDuration()))
            payload.put("duration", actionRequest.getDuration());

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-METHOD-", "POST");
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(payload));
        publishParam.put("-URI-", "/cpe-security/profiles/" + profileId + "/action");


        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", "POST");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.UPDATE_CPE_SECURITY_DEVICES, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    private HashMap<String, Object> generatePayloadForProfile(ProfileRuleRequest profileRuleRequest) {
        HashMap<String, Object> payload = new HashMap<>();
        if (Objects.nonNull(profileRuleRequest.getEnable()))
            payload.put("enable", profileRuleRequest.getEnable());
        if (Objects.nonNull(profileRuleRequest.getName()))
            payload.put("name", profileRuleRequest.getName());
        if (Objects.nonNull(profileRuleRequest.getCategories()))
            payload.put("categories", profileRuleRequest.getCategories());
        if (Objects.nonNull(profileRuleRequest.getDailyLimit()))
            payload.put("dailyLimit", profileRuleRequest.getDailyLimit());
        if (Objects.nonNull(profileRuleRequest.getBonus()))
            payload.put("bonus", profileRuleRequest.getBonus());
        if (Objects.nonNull(profileRuleRequest.getAvatarURL()))
            payload.put("avatarURL", profileRuleRequest.getAvatarURL());
        if (Objects.nonNull(profileRuleRequest.getSchedules())) {
            List<HashMap<String, Object>> listOfSchedule = new ArrayList<>();
            profileRuleRequest.getSchedules().forEach(schedule -> {
                HashMap<String, Object> schedulePayload = new HashMap<>();
                if (Objects.nonNull(schedule.getEnable()))
                    schedulePayload.put("enable", schedule.getEnable());
                if (Objects.nonNull(schedule.getTimeend()))
                    schedulePayload.put("timeend", schedule.getTimeend());
                if (Objects.nonNull(schedule.getTimestart()))
                    schedulePayload.put("timestart", schedule.getTimestart());
                if (Objects.nonNull(schedule.getDay()))
                    schedulePayload.put("day", schedule.getDay());

                if (!schedulePayload.keySet().isEmpty()) {
                    listOfSchedule.add(schedulePayload);
                }
            });
            payload.put("schedules", listOfSchedule);
        }

        return payload;
    }

    @Auditable(operation = AuditorConstants.PROFILE_CREATE, method = RequestMethod.POST)
    @Cachable(method = RequestMethod.POST, operation = FsecureConstants.PROFILES)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object createFSecureProfile(String equipmentIdOrSerialOrSTN, ProfileRuleRequest profileRuleRequest, HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-URI-", "/cpe-security/profiles");
        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(generatePayloadForProfile(profileRuleRequest)));
        publishParam.put("-METHOD-", "POST");

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/profiles");
        data.put("method", "POST");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.PROFILE, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.PROFILE_BY_ID)
    @Auditable(operation = AuditorConstants.PROFILE_UPDATE, method = RequestMethod.PUT)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object updateFSecureProfile(String equipmentIdOrSerialOrSTN, String profileId, ProfileRuleRequest profileRuleRequest, HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-URI-", "/cpe-security/profiles/" + profileId);
        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(generatePayloadForProfile(profileRuleRequest)));
        publishParam.put("-METHOD-", "PUT");

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", "PUT");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.PROFILE, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.PROFILE_DELETE, method = RequestMethod.DELETE)
    @Cachable(method = RequestMethod.DELETE, operation = FsecureConstants.PROFILE_BY_ID)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object deleteFSecureProfile(String equipmentIdOrSerialOrSTN, String profileId ,  HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-URI-", "/cpe-security/profiles/" + profileId);

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", "DELETE");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.DELETE_PROFILE, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }


    @Auditable(operation = AuditorConstants.GET_PROFILE_BY_ID, method = RequestMethod.GET, persist = false)
    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.PROFILE_BY_ID)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getFSecureProfileByID(String equipmentIdOrSerialOrSTN, String profileId, HttpServletRequest httpServletRequest) throws Exception {
        return getFSecureProfileByName(equipmentIdOrSerialOrSTN, profileId);
    }

    @Auditable(operation = AuditorConstants.GET_ALL_PROFILES, method = RequestMethod.GET, persist = false)
    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.PROFILES)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getAllFSecureProfile(String equipmentIdOrSerialOrSTN, String profileId, HttpServletRequest httpServletRequest) throws Exception {
        return getFSecureProfileByName(equipmentIdOrSerialOrSTN, profileId);
    }

    private Object getFSecureProfileByName( String equipmentIdOrSerialOrSTN, String profileId) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        if (Objects.nonNull(profileId))
            publishParam.put("-URI-", "/cpe-security/profiles/" + profileId);
        else
            publishParam.put("-URI-", "/cpe-security/profiles");

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());


        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.GET_PROFILE, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }


    @Auditable(operation = AuditorConstants.GET_PROTECTION_POLICIES, method = RequestMethod.GET, persist = false)
    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.PROTECTION)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getCPESecurityProtection(String equipmentIdOrSerialOrSTN,  HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/protection");
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.GET_CPE_SECURITY_PROTECTION, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.PROTECTION_UPDATE, method = RequestMethod.PUT)
    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.PROTECTION)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object updateCPESecurityProtection(String equipmentIdOrSerialOrSTN, ProtectionRequest protectionRequest,HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();

        HashMap<String, Object> payload = new HashMap<>();
        if (Objects.nonNull(protectionRequest.getIot()))
            payload.put("iot", protectionRequest.getIot());
        if (Objects.nonNull(protectionRequest.getBrowsing()))
            payload.put("browsing", protectionRequest.getBrowsing());
        if (Objects.nonNull(protectionRequest.getTracking()))
            payload.put("tracking", protectionRequest.getTracking());

        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(payload));

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/protection");
        data.put("method", "PUT");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());


        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.PUT_CPE_SECURITY_PROTECTION, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.GET_WEBSITE_EXCEPTIONS, method = RequestMethod.GET, persist = false)
    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.WEBSITE_EXCEPTION)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getCPESecurityWebSiteException(String equipmentIdOrSerialOrSTN,   HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/websiteException");
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.GET_CPE_SECURITY_WEB_EXCEPTION, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.WEBSITE_EXCEPTION_UPDATE, method = RequestMethod.PUT)
    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.WEBSITE_EXCEPTION)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public void updateCPESecurityWebSiteException(String equipmentIdOrSerialOrSTN, List<WebsiteExceptionRequest> websiteExceptionRequests,HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        List<HashMap<String, Object>> request = new ArrayList<>();
        if (Objects.nonNull(websiteExceptionRequests)) {
            websiteExceptionRequests.forEach(element -> {
                HashMap<String, Object> payload = new HashMap<>();
                if (Objects.nonNull(element.getPolicy()))
                    payload.put("policy", element.getPolicy());
                if (Objects.nonNull(element.getUrl()))
                    payload.put("url", element.getUrl());

                request.add(payload);
            });
        }

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(request));

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/websiteException");
        data.put("method", "PUT");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.PUT_CPE_SECURITY_WEB_EXCEPTION, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.WEBSITE_EXCEPTION_DELETE, method = RequestMethod.DELETE)
    @Cachable(method = RequestMethod.DELETE, operation = FsecureConstants.WEBSITE_EXCEPTION)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public void deleteCPESecurityWebSiteException(String equipmentIdOrSerialOrSTN , HttpServletRequest httpServletRequest ) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/websiteException");
        data.put("method", "DELETE");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.DELETE_CPE_SECURITY_WEB_EXCEPTION, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.GET_ALL_DEVICE, method = RequestMethod.GET, persist = false)
    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.DEVICES)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getAllCPESecurityDevices(String equipmentIdOrSerialOrSTN, String deviceId, HttpServletRequest httpServletRequest) throws Exception {
        return getCPESecurityDevices(equipmentIdOrSerialOrSTN, deviceId);
    }


    @Auditable(operation = AuditorConstants.GET_DEVICE_BY_ID, method = RequestMethod.GET, persist = false)
    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.DEVICE_BY_ID)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getCPESecurityDeviceById(String equipmentIdOrSerialOrSTN, String deviceId, HttpServletRequest httpServletRequest) throws Exception {
        return getCPESecurityDevices(equipmentIdOrSerialOrSTN, deviceId);
    }

    private Object getCPESecurityDevices(String equipmentIdOrSerialOrSTN, String deviceId) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        if (Objects.nonNull(deviceId))
            publishParam.put("-URI-", "/cpe-security/devices/" + deviceId);
        else
            publishParam.put("-URI-", "/cpe-security/devices");

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.GET_CPE_SECURITY_DEVICES, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.DEVICE_UPDATE, method = RequestMethod.PUT)
    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.DEVICE_BY_ID)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object updateCPESecurityDevices(String equipmentIdOrSerialOrSTN, String deviceId, DeviceDataRequest deviceDataRequest,HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }

        HashMap<String, Object> payload = new HashMap<>();
        if (Objects.nonNull(deviceDataRequest.getProfileId()))
            payload.put("profileId", deviceDataRequest.getProfileId());
        if (Objects.nonNull(deviceDataRequest.getFriendlyName()))
            payload.put("friendlyName", deviceDataRequest.getFriendlyName());
        if (Objects.nonNull(deviceDataRequest.getProtection())) {
            DeviceDataProtection deviceProtection = deviceDataRequest.getProtection();

            HashMap<String, Object> protection = new HashMap<>();
            if (Objects.nonNull(deviceProtection.getBlockInternet())) {
                protection.put("blockInternet", Boolean.valueOf(deviceProtection.getBlockInternet()));
            }

            if (Objects.nonNull(deviceProtection.getBrowsing())) {
                protection.put("browsing", Boolean.valueOf(deviceProtection.getBrowsing()));
            }

            if (Objects.nonNull(deviceProtection.getIot())) {
                protection.put("iot", Boolean.valueOf(deviceProtection.getIot()));
            }

            if (Objects.nonNull(deviceProtection.getTracking())) {
                protection.put("tracking", Boolean.valueOf(deviceProtection.getTracking()));
            }

            if (!protection.keySet().isEmpty())
                payload.put("protection", protection);
        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-METHOD-", "PUT");
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(payload));
        publishParam.put("-URI-", "/cpe-security/devices/" + deviceId);


        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", "PUT");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.UPDATE_CPE_SECURITY_DEVICES, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.DEVICE_ACTION_CREATE, method = RequestMethod.POST)
    @Cachable(method = RequestMethod.POST,operation = DEVICE_TRIGGER_ACTION)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object triggerActionForDevice(String equipmentIdOrSerialOrSTN, String deviceId, DeviceActionRequest actionRequest,HttpServletRequest httpServletRequest) throws Exception {


        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, Object> payload = new HashMap<>();
        if (Objects.nonNull(actionRequest.getAction()))
            payload.put("action", actionRequest.getAction());
        if (Objects.nonNull(actionRequest.getDelay()))
            payload.put("delay", actionRequest.getDelay());
        if (Objects.nonNull(actionRequest.getDuration()))
            payload.put("duration", actionRequest.getDuration());

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-METHOD-", "POST");
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(payload));
        publishParam.put("-URI-", "/cpe-security/devices/" + deviceId + "/action");


        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", "POST");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.UPDATE_CPE_SECURITY_DEVICES, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.REGISTRATION_UPDATE, method = RequestMethod.PUT)
//    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.REGISTRATION)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object setRegistrationKey(String equipmentIdOrSerialOrSTN, RegistrationRequest registrationRequest,HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        HashMap<String, String> queryParam = new HashMap<>();
        queryParam.put("userId", equipment.getRgwSerial());
        queryParam.put("serialNumber", equipment.getRgwSerial());
        DBObject apDetail = mongoService.findOne(AP_DETAIL, queryParam);
        String isp = "";
        if (Objects.isNull(apDetail)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Unable to fetch ISP for " + equipment.getRgwSerial() + " RGW.");
        }
        isp = (String) apDetail.get("isp");
        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(registrationRequest));
        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());
        publishParam.put("-ISP-", isp);

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/registration");
        data.put("method", "PUT");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.PUT_CPE_SECURITY_REGISTRATION, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }


    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.REGISTRATION)
    @Auditable(operation = AuditorConstants.GET_REGISTRATION_KEY, method = RequestMethod.GET, persist = false)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getRegistrationKey(String equipmentIdOrSerialOrSTN, HttpServletRequest httpServletRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-METHOD-", "GET");

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/registration");
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.CPE_SECURITY_REGISTRATION, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }


    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object deleteRegistrationKey(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN)) {
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-METHOD-", "DELETE");

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/registration");
        data.put("method", "DELETE");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.CPE_SECURITY_REGISTRATION, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.EVENTS)
    @Auditable(operation = AuditorConstants.GET_ALL_EVENTS, method = RequestMethod.GET, persist = false)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getCPESecurityEvents(String equipmentIdOrSerialOrSTN, String limit, String after, HttpServletRequest httpServletRequest) throws Exception {
        Integer limitRecord = null;
        if (Objects.nonNull(limit)) {
            try {
                limitRecord = Integer.valueOf(limit);
                if (limitRecord < 0)
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value passed for limit");
            } catch (NumberFormatException e) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value passed for limit");
            } catch (Exception ex) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value passed for limit");
            }
        }

//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN)) {
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        if (Objects.nonNull(after) && Objects.nonNull(limitRecord)) {
            publishParam.put("-URI-", "/cpe-security/events" + "?after=" + after + "&limit=" + limitRecord);
        } else if (Objects.nonNull(after) && Objects.isNull(limitRecord)) {
            publishParam.put("-URI-", "/cpe-security/events" + "?after=" + after);
        } else if (Objects.nonNull(limitRecord) && Objects.isNull(after)) {
            publishParam.put("-URI-", "/cpe-security/events" + "?limit=" + limitRecord);
        } else {
            publishParam.put("-URI-", "/cpe-security/events");
        }

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.GET_CPE_SECURITY_EVENTS, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.EVENTS_DELETE, method = RequestMethod.DELETE)
    @Cachable(method = RequestMethod.DELETE, operation = FsecureConstants.EVENTS)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object deleteCPESecurityEvents(String equipmentIdOrSerialOrSTN, HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
        List<String> urlList = Arrays.asList("/cpe-security/events", "/cpe-security/events/categoryCounters");
        String eventTid = null;
        HashMap<String, String> publishParam = new HashMap<>();
        for(String uri: urlList) {
            publishParam.clear();
            String tid = CommonUtils.generateUUID();
            publishParam.put("-TID-", tid);
            publishParam.put("-URI-", uri);
            if (uri.equals("/cpe-security/events"))
                eventTid = tid;

            publishParam.put("-USER_ID-", equipment.getRgwSerial());
            publishParam.put("-S_ID-", equipment.getRgwSerial());

            HashMap<String, Object> data = new HashMap<>();
            data.put("_id", tid);
            data.put("uri", publishParam.get("-URI-"));
            data.put("method", "DELETE");
            data.put("isTimeout", false);
            data.put("userId", equipment.getRgwSerial());
            data.put("dateCreated", new Date());

            mongoService.create(JSON_RPC_V3_INFO, data);

            rpcUtilityService.publishToTopic(publishParam, MqttTemplate.DELETE_CPE_SECURITY_EVENTS, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);
        }

        return manageCommonService.processFSecureRPCResult(eventTid, max_Tries, THREAD_TO_SLEEP);
    }

    @Auditable(operation = AuditorConstants.GET_EVENTS_COUNTERS, method = RequestMethod.GET, persist = false)
    @OperationHandle(operation = OperationConstants.FSECURE_OP)
    public Object getCPESecurityCategoryCounters(String equipmentIdOrSerialOrSTN,HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN)) {
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-METHOD-", "GET");

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-security/events/categoryCounters");
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.CATEGORY_COUNTERS, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    public Object getAllProfileCategories() {
        return Arrays.asList(ProfileCategories.values());
    }
}
