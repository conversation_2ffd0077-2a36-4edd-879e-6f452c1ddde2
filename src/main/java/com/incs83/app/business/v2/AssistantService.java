package com.incs83.app.business.v2;

/**
 * Created by hari on 28/6/17.
 */
//@Service
/*
public class AssistantService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private RPCUtilityService rPCUtilityService;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private Environment environment;

    @Autowired
    private HttpServiceImpl httpService;

    @Autowired
    private ManageProfileService manageProfileService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private ManageSubscriberNetworkStatsService manageSubscriberNetworkStatsService;

    @Autowired
    private ManageEquipmentService manageEquipmentService;

    @Autowired
    private CommonService commonService;

    public HashMap<String, Object> processAssistantVoiceCommand(String assistant, HashMap requestParams, String accessToken) throws Exception {
        HashMap<String, Object> responseMap = new HashMap<>();
        try {
            JSONObject assistantRequest = new JSONObject(requestParams);
            if (!accessToken.equals(ApplicationConstants.VOICE_ACCESS_TOKEN)) {
                responseMap.put("speech", "Not authenticated to make this request");
                responseMap.put("displayText", "Not authenticated to make this request");
                return responseMap;
            } else {
                String actionOn = null;
                String email;
                String duration = null;
                String intentName;
                if (assistant.equalsIgnoreCase(GOOGLE_ASSISTANT)) {
                    HashMap<String, Object> queryParams = new HashMap<>();
                    queryParams.put("access_token", assistantRequest.getJSONObject("originalRequest").getJSONObject("data").getJSONObject("user").getString("accessToken"));
                    String resp = httpService.doGet(GOOGLE_USER_ACCOUNT_INFO, new HashMap<>(), queryParams);
                    JSONObject userData = new JSONObject(resp);
                    email = userData.getString("email");
                    intentName = assistantRequest.getJSONObject("result").getJSONObject("metadata").getString("intentName");
                    String[] intentNames = intentName.split(" ");
                    intentName = intentNames.length > 0 ? intentNames[0] : intentName;
                    if (intentName.equals(HEALTH_SCORE)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else if (intentName.equals(REBOOT_EQUIPMENT)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else if (intentName.equals(WAN_SPEED_TEST)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else if (intentName.equals(OPTIMIZE_AP)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else if (intentName.equals(BAND_WIDTH_UTILIZATION)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else {
                        actionOn = assistantRequest.getJSONObject("result").getJSONObject("parameters").optString("profile_name");
                        duration = assistantRequest.getJSONObject("result").getJSONObject("parameters").optJSONObject("duration") == null ? "0" : assistantRequest.getJSONObject("result").getJSONObject("parameters").optJSONObject("duration").getString("amount");
                    }
                    responseMap = processAssistantVoiceCommand(actionOn, email, duration, intentName, ApplicationConstants.GOOGLE);
                } else if (assistant.equalsIgnoreCase(AMAZON_ASSISTANT)) {
                    email = assistantRequest.getJSONObject("user").getString("mail");
                    intentName = assistantRequest.getJSONObject("request").getString("intent");
                    if (intentName.equals(HEALTH_SCORE)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else if (intentName.equals(REBOOT_EQUIPMENT)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else if (intentName.equals(WAN_SPEED_TEST)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else if (intentName.equals(OPTIMIZE_AP)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else if (intentName.equals(BAND_WIDTH_UTILIZATION)) {
                        actionOn = ApplicationConstants.EMPTY_STRING;
                        duration = ApplicationConstants.EMPTY_STRING;
                    } else {
                        if (Objects.nonNull(assistantRequest.getJSONObject("request"))) {
                            if (Objects.nonNull(assistantRequest.getJSONObject("request").optJSONObject("slots"))) {
                                actionOn = assistantRequest.getJSONObject("request").optJSONObject("slots").optString("profile_name");
                                if (Objects.isNull(actionOn))
                                    actionOn = ApplicationConstants.EMPTY_STRING;
                            }

                            if (Objects.nonNull(assistantRequest.getJSONObject("request").optJSONObject("slots"))) {
                                if (Objects.nonNull(assistantRequest.getJSONObject("request").optJSONObject("slots").optJSONObject("duration"))) {
                                    duration = assistantRequest.getJSONObject("request").optJSONObject("slots").optJSONObject("duration").optString("amount");
                                    if (Objects.isNull(duration))
                                        duration = ApplicationConstants.EMPTY_STRING;
                                }
                            }
                        }
                    }
                    responseMap = processAssistantVoiceCommand(actionOn, email, duration, intentName, ApplicationConstants.ALEXA);
                    return responseMap;
                }

                return responseMap;
            }
        } catch (Exception e) {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);
            return responseMap;
        }
    }

    private HashMap processAssistantVoiceCommand(String actionOn, String email, String duration, String intentName, String type) throws Exception {
        HashMap<String, Object> responseMap = new HashMap<>();
        String name;
        UserAP userAP;
        String subscriberId;

        String loggedInUserId = null;
        String userCompartmentId = null;
        String userRoleName = null;

        HashMap<String, String> headerInfo = new HashMap<>();
        headerInfo.put("roleName", userRoleName);
        headerInfo.put("compartmentId", userCompartmentId);
        headerInfo.put("id", loggedInUserId);

        HashMap<String, Object> searchParams = new HashMap<>();
        searchParams.put("email", email);
        searchParams.put("type", type);

        String url = environment.getProperty(ApplicationConstants.INTERNAL_DNS) + MICROSERVICE_CDS + "/users/search";

        String httpResponse;

        httpResponse = httpService.doGet(url, headerInfo, searchParams);

        ObjectMapper mapper = new ObjectMapper();
        HashMap response = mapper.readValue(httpResponse, HashMap.class);


        if (Integer.valueOf(response.get("code").toString()) != 0) {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);

            return responseMap;
        }

        if (Objects.nonNull(response.get("data"))) {
            List<LinkedHashMap> responseData = (List<LinkedHashMap>) response.get("data");
            if (responseData.isEmpty()) {
                responseMap.put("speech", "Sorry, no User found with email address " + email);
                responseMap.put("displayText", "Sorry, no User found with email address " + email);
                return responseMap;
            } else {
                if (Objects.nonNull(responseData.get(0).get("id"))) {
                    subscriberId = String.valueOf(responseData.get(0).get("id"));
                    name = String.valueOf(responseData.get(0).get("firstName")) + " " + String.valueOf(responseData.get(0).get("lastName"));
                    userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(subscriberId);
                    if (type.equalsIgnoreCase(ApplicationConstants.ALEXA)) {
                        if (!(userAP.getAlexaVoiceSubscribed())) {
                            responseMap.put("speech", "Sorry " + name + ", You are not entitled for this service.");
                            responseMap.put("displayText", "Sorry " + name + ", You are not entitled for this service.");

                            return responseMap;
                        }
                    } else {
                        if (!(userAP.getGoogleHomeVoiceSubscribed())) {
                            responseMap.put("speech", "Sorry " + name + ", You are not entitled to this service.");
                            responseMap.put("displayText", "Sorry " + name + ", You are not entitled to this service.");

                            return responseMap;
                        }
                    }
                } else {
                    responseMap.put("speech", "Sorry no Subscriber found with email address " + email);
                    responseMap.put("displayText", "Sorry no Subscriber found with email address " + email);
                    return responseMap;
                }

            }
        } else {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);

            return responseMap;
        }

        switch (intentName) {
            case HEALTH_SCORE:
                responseMap = getWifiHealthScoreForUser(subscriberId, name);
                break;

            case REMOVE_PROFILE:
                responseMap = processPauseUnPauseProfile(actionOn, subscriberId, name, duration, PAUSE);
                break;

            case ADD_PROFILE:
                responseMap = processPauseUnPauseProfile(actionOn, subscriberId, name, duration, UNPAUSE);
                break;

            case REBOOT_EQUIPMENT:
                responseMap = processAPReboot(subscriberId, name);
                break;

            case WAN_SPEED_TEST:
                responseMap = processWanSpeedTest(subscriberId, name);
                break;

            case OPTIMIZE_AP:
                responseMap = processApOptimization(userAP, ActiontecConstants.OBJECT_TYPE_DOACS);
                break;

            case BAND_WIDTH_UTILIZATION:
                responseMap = processToFetchTopProfiles(subscriberId, name);
                break;
            case DISABLE_GUEST_WIFI:
                responseMap = processDisableGuest(userAP);
                break;
        }

        return responseMap;
    }

    private HashMap getWifiHealthScoreForUser(String subscriberId, String customerName) {
        HashMap<String, Object> responseMap = new HashMap<>();
        try {
            WifiMeterDataDTO wifiMeterDataDTO = manageSubscriberNetworkStatsService.getWifiHealthScore(subscriberId);
            double score = wifiMeterDataDTO.getData();
            responseMap.put("speech", "Hey " + customerName + ", your wifi health score is " + score);
            responseMap.put("displayText", "Hey " + customerName + ", your wifi health score is " + score);
            responseMap.put("wifiScore", Double.parseDouble(TWO_DECIMAL_PLACE.format(score)));

            return responseMap;

        } catch (Exception e) {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);
            return responseMap;
        }
    }

    private HashMap processAPReboot(String subscriberId, String subscriberName) {
        HashMap<String, Object> responseMap = new HashMap<>();
        try {
            EquipmentListDTO equipmentListDTO = (EquipmentListDTO) manageEquipmentService.getAllEquipmentsForSubscriber(subscriberId).get("equipmentDetails");
            if (Objects.nonNull(equipmentListDTO.getData()) && !equipmentListDTO.getData().isEmpty()) {
                ArrayList<EquipmentListDTO.EquipmentListData> equipmentListData = equipmentListDTO.getData();
                EquipmentListDTO.EquipmentListData equipment = equipmentListData.stream().filter(element -> GATEWAY.equals(element.getType())).findAny().orElse(null);
                if (Objects.nonNull(equipment)) {
                    return performAPReboot(subscriberId, equipment, subscriberName);
                } else {
                    responseMap.put("speech", "Sorry No Equipment associated with Subscriber " + subscriberName);
                    responseMap.put("displayText", "Sorry No Equipment associated with Subscriber " + subscriberName);
                    return responseMap;
                }
            } else {
                responseMap.put("speech", "Sorry No Device associated with Subscriber " + subscriberName);
                responseMap.put("displayText", "Sorry No Device associated with Subscriber " + subscriberName);
                return responseMap;
            }

        } catch (Exception e) {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);
            return responseMap;
        }
    }

    private HashMap processWanSpeedTest(String subscriberId, String subscriberName) {
        HashMap<String, Object> responseMap = new HashMap<>();
        try {
            SpeedTestHistoryDTO speedTestHistoryDTO = manageSubscriberNetworkStatsService.performSpeedTestForUser(subscriberId, false);
            List<SpeedStats> data = speedTestHistoryDTO.getData();
            if (Objects.nonNull(data) && !data.isEmpty()) {
                SpeedStats speedStats = data.get(0);

                responseMap.put("speech", "Hey " + subscriberName + ", as per last speed test, your download speed was " + speedStats.getDownloadSpeed() + "Mbps, upload speed was " + speedStats.getUploadSpeed() + "Mbps and a latency of " + speedStats.getLatency() + "milliseconds.");
                responseMap.put("displayText", "Hey " + subscriberName + ", as per last speed test, your download speed was " + speedStats.getDownloadSpeed() + "Mbps, upload speed was " + speedStats.getUploadSpeed() + "Mbps and a latency of " + speedStats.getLatency() + "milliseconds.");
                return responseMap;
            } else {
                responseMap.put("speech", "No information is available this time");
                responseMap.put("displayText", "No information is available this time");
                return responseMap;
            }
        } catch (ValidationException vEx) {
            responseMap.put("speech", "Sorry, " + vEx.getMessage());
            responseMap.put("displayText", "Sorry, " + vEx.getMessage());
            return responseMap;
        } catch (Exception e) {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);
            return responseMap;
        }
    }

    private HashMap processToFetchTopProfiles(String subscriberId, String subscriberName) {
        HashMap<String, Object> responseMap = new HashMap<>();
        try {
            ProfileListDTO profileListDTO = manageProfileService.getTopFiveProfiles(subscriberId, HOURLY_DATA);
            ArrayList<ProfileListDTO.ProfileListData> data = profileListDTO.getData();
            if (Objects.nonNull(data) && !data.isEmpty()) {
                Collections.sort(data);
                Collections.reverse(data);
                ProfileListDTO.ProfileListData topProfile = data.get(0);

                responseMap.put("speech", "Hey " + subscriberName + ", " + topProfile.getProfileName() + " is consuming the maximum bandwidth.");
                responseMap.put("displayText", "Hey " + subscriberName + ", " + topProfile.getProfileName() + " is consuming the maximum bandwidth.");
                return responseMap;
            } else {
                responseMap.put("speech", "No profile information is available this time.");
                responseMap.put("displayText", "No profile information is available this time.");
                return responseMap;
            }

        } catch (Exception e) {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);
            return responseMap;
        }
    }

    private HashMap processDisableGuest(UserAP userAP) throws Exception {
        HashMap<String, Object> responseMap = new HashMap<>();
        try {
            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);
            HashMap<String, String> queryParams = new HashMap<>();
            HashMap<String, String> appendableParams = new HashMap<>();
            queryParams.put("userId", userAP.getApId());
            queryParams.put("serialNumber", userAP.getApId());
            DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
            List<BasicDBObject> _24gRadio = (List<BasicDBObject>) aPDetails.get("bssid24G");
            List<BasicDBObject> _5gRadio = (List<BasicDBObject>) aPDetails.get("bssid5G");
            if (_24gRadio.get(1).getInt("enabled") == 0 || _5gRadio.get(1).getInt("enabled") == 0) {
                responseMap.put("speech", "Guest Wifi is already disabled for your account.");
                responseMap.put("displayText", "Guest Wifi is already disabled for your account.");
                return responseMap;
            }
//            manageCommonService.isEquipmentOffline(userAP.getApId(), userAP);

            HashMap<String, String> publishParams = new HashMap<>();
            String tid = CommonUtils.generateUUID();
            publishParams.put("-USER_ID-", userAP.getApId());
            publishParams.put("-S_ID-", userAP.getApId());
            publishParams.put("-ID-", String.valueOf(Calendar.getInstance().getTimeInMillis()));
            publishParams.put("-TID-", tid);
            publishParams.put("-TYPE-", "2,6");
            publishParams.put("-ENABLE-", "0");
            HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
            Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
            rPCUtilityService.publishToTopic(publishParams, CHANGE_NETWORK_STATE, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);


            responseMap.put("speech", "Guest Wifi has been successfully disabled. It may take a minute for the changes to be effective.");
            responseMap.put("displayText", "Guest Wifi has been successfully disabled. It may take a minute for the changes to be effective.");
            return responseMap;
        } catch (ValidationException vEx) {
            responseMap.put("speech", "Sorry, " + vEx.getMessage());
            responseMap.put("displayText", "Sorry, " + vEx.getMessage());
            return responseMap;
        }
    }

    private HashMap processApOptimization(UserAP userAP, String objectType) {
        HashMap<String, Object> responseMap = new HashMap<>();
        try {
//            manageCommonService.isEquipmentOffline(userAP.getApId(), userAP);


            String tid = CommonUtils.generateUUID();

            HashMap<String, Object> data = new HashMap<>();
            data.put("userId", userAP.getApId());
            data.put("_id", tid);

            mongoService.create(CHANNEL_OPTIMIZATION, data);

            HashMap<String, String> publishParams = new HashMap<>();
            publishParams.put("-USER_ID-", userAP.getApId());
            publishParams.put("-S_ID-", userAP.getApId());
            publishParams.put("-ID-", String.valueOf(Calendar.getInstance().getTimeInMillis()));
            publishParams.put("-TID-", tid);
            publishParams.put("-RADIO-", String.valueOf(ActiontecConstants._24GRadio));
            publishParams.put("-OBJECT-", objectType);
            HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
            Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
            rPCUtilityService.publishToTopic(publishParams, MqttTemplate.DIRECTED_HEAL_SELF_HEAL_TEMPLATE_FOR_USER, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);


            boolean resultReceived = false;
            int maxTries = 0;

            BasicDBObject query = new BasicDBObject();
            query.put("_id", tid);

            while (!resultReceived) {
                try {
                    Thread.sleep(THREAD_TO_SLEEP);
                } catch (InterruptedException e) {
                    LOG.error("Error during thread sleep", e);
                }
                maxTries++;
                if (maxTries == max_Tries) {
                    LOG.error("Operation Timed Out...please retry");

                    BasicDBObject dataToUpdate = new BasicDBObject();
                    dataToUpdate.put("result", TIME_OUT);
                    dataToUpdate.put("date", new Date());

                    BasicDBObject update = new BasicDBObject();
                    update.put("$set", dataToUpdate);
                    mongoService.update(query, update, false, false, CHANNEL_OPTIMIZATION);
                    break;
                }
                DBObject channelOptimization = mongoService.findOne(CHANNEL_OPTIMIZATION, query);
                if (Objects.isNull(channelOptimization) || Objects.isNull(channelOptimization.get("date"))) {
                    continue;
                } else {
                    resultReceived = true;
                }
            }
            DBObject channelOptimization = mongoService.findOne(CHANNEL_OPTIMIZATION, query);
            if (Objects.nonNull(channelOptimization)) {
                if (!RPC_RESULT.equals(channelOptimization.get("result"))) {
                    responseMap.put("speech", "Operation failed, please try again after sometime.");
                    responseMap.put("displayText", "Operation failed, please try again after sometime.");
                    return responseMap;
                }
            }


            responseMap.put("speech", "Channel Scan for the equipment has been triggered. There will be a slight interruption in connectivity; post which the connectivity will improve.");
            responseMap.put("displayText", "Channel Scan for the equipment has been triggered. There will be a slight interruption in connectivity; post which the connectivity will improve.");
            return responseMap;
        } catch (ValidationException vEx) {
            responseMap.put("speech", "Sorry, " + vEx.getMessage());
            responseMap.put("displayText", "Sorry, " + vEx.getMessage());
            return responseMap;
        } catch (Exception e) {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);
            return responseMap;
        }
    }

    private HashMap performAPReboot(String subscriberId, EquipmentListDTO.EquipmentListData equipment, String subscriberName) {
        HashMap<String, Object> responseMap = new HashMap<>();
        try {
            manageEquipmentService.invokeInternetRPCMethodsForEquipment(subscriberId, equipment.getSerialNumber(), AP_REBOOT, null, null);

            responseMap.put("speech", equipment.getName() + " has been rebooted successfully, it may take a while for the internet connectivity to be restored.");
            responseMap.put("displayText", equipment.getName() + " has been rebooted successfully, it may take a while for the internet connectivity to be restored.");
            return responseMap;
        } catch (ValidationException vEx) {
            responseMap.put("speech", "Sorry, " + vEx.getMessage());
            responseMap.put("displayText", "Sorry, " + vEx.getMessage());
            return responseMap;
        } catch (Exception e) {
            responseMap.put("speech", "Sorry " + equipment.getName() + " device is not associated with Subscriber " + subscriberName);
            responseMap.put("displayText", "Sorry " + equipment.getName() + " device is not associated with Subscriber " + subscriberName);
            return responseMap;
        }
    }

    private HashMap processPauseUnPauseProfile(String profileName, String subscriberId, String name, String duration, String intentName) {
        HashMap<String, Object> responseMap = new HashMap<>();
        DBObject profileObject = new BasicDBObject();
        try {
            SubscriberProfileListDTO subscriberProfileListDTO = manageProfileService.fetchAllUserProfiles(subscriberId);
            List<SubscriberProfileDTO.SubscriberProfileData> subscriberProfileDataList = subscriberProfileListDTO.getData();

            for (SubscriberProfileDTO.SubscriberProfileData subscriberProfileData : subscriberProfileDataList) {
                if (CommonUtils.isMatched(profileName.toLowerCase(), subscriberProfileData.getProfileName().toLowerCase())) {
                    profileName = subscriberProfileData.getProfileName();
                    if (subscriberProfileData.getDevices().isEmpty()) {
                        responseMap.put("speech", "Sorry, no device is attached to profile " + profileName + ", so no action to take.");
                        responseMap.put("displayText", "Sorry, no device is attached to profile " + profileName + ", so no action to take.");
                        return responseMap;
                    }

                    List<String> profileDevices = new ArrayList<>();
                    List<ProfileDevicesDTO> devices = subscriberProfileData.getDevices();
                    for (ProfileDevicesDTO profileDevicesDTO : devices) {
                        profileDevices.add(profileDevicesDTO.getMac());
                    }

                    profileObject.put("id", subscriberProfileData.getId());
                    profileObject.put("subscriberId", subscriberProfileData.getSubscriberId());
                    profileObject.put("devices", profileDevices);

                    break;
                } else {
                    responseMap.put("speech", "Sorry, please try again with a valid profile name.");
                    responseMap.put("displayText", "Sorry, please try again with a valid profile name.");
                    return responseMap;
                }
            }
        } catch (Exception e) {
            responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
            responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);
            return responseMap;
        }

        if (duration.equals(EMPTY_STRING)) {
            try {
                if (manageProfileService.performProfilePauseUnPause(profileObject, intentName)) {
                    BasicDBObject insertField = new BasicDBObject();
                    insertField.put("currentState", intentName);
                    insertField.put("onDemandState", intentName);
                    insertField.put("timedAccess", null);
                    BasicDBObject basicDBObjectUpdate = new BasicDBObject();
                    basicDBObjectUpdate.put("$set", insertField);
                    HashMap<String, Object> params = new HashMap<>();
                    params.put("id", profileObject.get("id"));
                    mongoService.findAndModify(params, USER_PROFILE, TIMESTAMP, basicDBObjectUpdate);

                    responseMap.put("speech", "Hey " + name + ", " + profileName + " has been " + (intentName.equals("pause") ? intentName + "d indefinitely." : intentName + "d."));
                    responseMap.put("displayText", "Hey " + name + ", " + profileName + " has been " + (intentName.equals("pause") ? intentName + "d indefinitely." : intentName + "d."));

                    return responseMap;
                } else {
                    responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
                    responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);

                    return responseMap;
                }
            } catch (Exception e) {
                responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
                responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);

                return responseMap;
            }

        } else {
            long durationForPause = Long.valueOf(duration);
            try {
                manageProfileService.pauseUnpauseForXMins(durationForPause, intentName, String.valueOf(profileObject.get("id")), true);
                responseMap.put("speech", "Hey " + name + ", " + profileName + " has been " + intentName + "d for " + durationForPause + " minutes.");
                responseMap.put("displayText", "Hey " + name + ", " + profileName + " has been " + intentName + "d for " + durationForPause + " minutes.");

                return responseMap;
            } catch (Exception e) {
                responseMap.put("speech", ActiontecConstants.ERROR_VOICE_MESSAGE);
                responseMap.put("displayText", ActiontecConstants.ERROR_VOICE_MESSAGE);

                return responseMap;
            }
        }
    }
}
*/
