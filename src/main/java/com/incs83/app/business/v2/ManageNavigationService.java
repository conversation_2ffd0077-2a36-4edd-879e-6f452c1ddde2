package com.incs83.app.business.v2;

import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.common.v2.UpdateMenuRequest;
import com.incs83.app.common.v2.UpdateRoleMenuMapping;
import com.incs83.app.constants.queries.NavigationSQL;
import com.incs83.app.entities.MenuItems;
import com.incs83.app.responsedto.v2.navigation.NavigationDTO;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.queries.NavigationSQL.DELETE_ROLE_MENU_MAPPING;
import static com.incs83.app.constants.queries.NavigationSQL.INSERT_ROLE_MAPPING;

@Service
public class ManageNavigationService {

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private IAMServices IAMServices;

    @Autowired
    private HazelcastService cacheService;

    private static final Logger LOG = LogManager.getLogger("org");

    public List<NavigationDTO> getNavigation() throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("roleId", CommonUtils.getRoleIdOfLoggedInUser());
        LOG.info("roleId: {}", CommonUtils.getRoleIdOfLoggedInUser());
        HashMap<String, String> cacheParams = new HashMap<>();
        cacheParams.put("key", CommonUtils.getRoleIdOfLoggedInUser());
        cacheParams.put("map", NAVIGATION);
        return getNavigationFromSQLData(dataAccessService.readNative(NavigationSQL.GET_NAVIGATION, params));
    }

    public List<NavigationDTO> getAllMenuItems() throws Exception {
        return getNavigationFromSQLData(dataAccessService.readNative(NavigationSQL.GET_ALL_MENU, new HashMap<>()));

    }

    public List<MenuItems> upsertMenuItems(List<UpdateMenuRequest> updateMenuRequests) throws Exception {
        List<MenuItems> menusItems = new ArrayList<>();
        for (UpdateMenuRequest item : updateMenuRequests) {
            if (CustomStringUtils.isEmpty(item.getNavName())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The navName is empty.");
            }

            // parent menu must have url
            if (CustomStringUtils.isNotEmpty(item.getParentId()) && CustomStringUtils.isEmpty(item.getUrl())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The parent menu url is empty.");
            }
            
            if (CustomStringUtils.isEmpty(item.getId())) {
                LOG.info("create new menu!");

                MenuItems newMenuItem = new MenuItems();
                BeanUtils.copyProperties(item, newMenuItem);
                newMenuItem.setId(CommonUtils.generateUUID());
                newMenuItem.setEntities(null);
                newMenuItem.setCreatedAt(new LocalDateTime().toDate());
                newMenuItem.setCreatedBy(CommonUtils.getUserIdOfLoggedInUser());
                dataAccessService.create(MenuItems.class, newMenuItem);
            } else {
                MenuItems menu = (MenuItems) dataAccessService.read(MenuItems.class, item.getId());

                HashMap<String, Object> param = new HashMap<>();
                param.put("fontAwesomeIconClass", item.getFontAwesomeIconClass());
                param.put("navName", item.getNavName());
                param.put("newTab", item.isNewTab());
                param.put("position", item.getPosition());
                param.put("menu_id", menu.getId());
                param.put("parentId", item.getParentId());
                param.put("url", item.getUrl());
                dataAccessService.update(MenuItems.class, NavigationSQL.UPDATE_MENU, param);
            }
            cacheService.delete(CommonUtils.getRoleIdOfLoggedInUser(), NAVIGATION);
        }
        return menusItems;
    }

    public List<HashMap<String, Object>> updateRoleMenuMapping(UpdateRoleMenuMapping updateRoleMenuMapping) throws Exception {
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        cacheService.delete(CommonUtils.getRoleIdOfLoggedInUser(), NAVIGATION);
        cacheService.delete(CommonUtils.getGroupIdOfLoggedInUser(), ROLE);
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("role_id", updateRoleMenuMapping.getRoleId());
        dataAccessService.deleteUpdateNative(DELETE_ROLE_MENU_MAPPING, queryParams);
        queryParams.clear();
        updateRoleMenuMapping.getMenuId().forEach(item -> {
            try {
                queryParams.put("menuId", item);
                queryParams.put("roleId", updateRoleMenuMapping.getRoleId());
                dataAccessService.deleteUpdateNative(INSERT_ROLE_MAPPING, queryParams);
                queryParams.clear();
            } catch (Exception e) {
                LOG.error("Error update role menu mapping", e);
            }
        });
//        if (Objects.nonNull(updateRoleMenuMapping.getMenuId())) {
//            Set<String> entities = new HashSet<>();
//            entities.add("Common");
//            if (!updateRoleMenuMapping.getMenuId().isEmpty())
//                entities = generateRoleEntities(updateRoleMenuMapping.getMenuId());
//
//            RoleRequest roleRequest = new RoleRequest();
//            roleRequest.setEntities(entities);
//
//            List<String> permission = new ArrayList<>();
//            permission.add("ALL");
//
//            List<RoleEntitlement> roleEntitlementList = new ArrayList<>();
//            entities.forEach(entity -> {
//                try {
//                    RoleEntitlement roleEntitlement = new RoleEntitlement();
//                    roleEntitlement.setResource(entity);
//                    roleEntitlement.setPermissions(permission);
//                    roleEntitlementList.add(roleEntitlement);
//                } catch (Exception e) {
//                    LOG.error("Error while creating role entitlement", e);
//                }
//            });
//            roleRequest.setRoleEntitlement(roleEntitlementList);
//            IAMServices.updateRoleById(updateRoleMenuMapping.getRoleId(), roleRequest, true);
//        }
        return getMappingForRole(updateRoleMenuMapping.getRoleId());
    }

    private Set<String> generateRoleEntities(List<String> menuIds) {
        Set<String> entities = new HashSet<>();
        menuIds.forEach(menuId -> {
            try {
                MenuItems menuItems = (MenuItems) dataAccessService.read(MenuItems.class, menuId);
                if (Objects.nonNull(menuItems.getEntities())) {
                    String[] entityList = menuItems.getEntities().split(COMMA);
                    entities.addAll(Arrays.asList(entityList));
                }
            } catch (Exception e) {
                LOG.error("Error in generate role entities", e);
            }
        });

        return entities;
    }

    public List<HashMap<String, Object>> getMappingForRole(String roleId) throws Exception {
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        List<HashMap<String, Object>> mappingEntries = new ArrayList<>();
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("roleId", roleId);
        List<Object[]> mapping = dataAccessService.readNative(NavigationSQL.GET_MENU_FOR_ROLE, queryParams);
        List<Object[]> childItems = new ArrayList<>();
        List<Object[]> parentItems = new ArrayList<>();
        mapping.forEach(item -> {
            if (Objects.isNull(ValidationUtil.isNullString(String.valueOf(item[1])))) {
                parentItems.add(item);
            } else {
                childItems.add(item);
            }
        });
        parentItems.forEach(p -> {
            HashMap<String, Object> record = new HashMap<>();
            String parentId = String.valueOf(p[0]);
            record.put("id", parentId);
            record.put("subId", childItems.stream().filter(o -> o[1].equals(parentId)).collect(Collectors.toList()).stream().map(c -> String.valueOf(c[0])).collect(Collectors.toList()));
            mappingEntries.add(record);
        });
        return mappingEntries;
    }

    private List<NavigationDTO> getNavigationFromSQLData(List<Object[]> navItems) {
        List<NavigationDTO> navigation = new ArrayList<>();
        List<Object[]> childItems = new ArrayList<>();
        List<Object[]> parentItems = new ArrayList<>();
        navItems.forEach(item -> {
            if (Objects.isNull(ValidationUtil.isNullString(String.valueOf(item[2])))) {
                parentItems.add(item);
            } else {
                childItems.add(item);
            }
        });
        parentItems.forEach(p -> {
            String parentId = String.valueOf(p[1]);
            List<Object[]> childrenForParent = childItems.stream().filter(o -> o[2].equals(parentId)).collect(Collectors.toList());
            NavigationDTO menu = createMenuMapping(p, childrenForParent);
            navigation.add(menu);
        });
        return navigation;
    }

    private NavigationDTO createMenuMapping(Object[] parent, List<Object[]> child) {
        List<NavigationDTO> subMenus = new ArrayList<>();
        child.forEach(i -> subMenus.add(NavigationDTO.mapToNavigationDTO(i)));
        NavigationDTO menu = NavigationDTO.mapToNavigationDTO(parent);
        menu.setSubMenus(subMenus);
        return menu;
    }
}
