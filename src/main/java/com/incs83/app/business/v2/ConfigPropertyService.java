package com.incs83.app.business.v2;

import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.ConfigProperty;
import com.incs83.app.enums.DBToFetchSteeringLogs;
import com.incs83.app.responsedto.v2.System.ConfigPropertyDTO;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.request.ConfigPropertyRequest;
import com.incs83.service.CommonService;
import com.incs83.services.HazelcastService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Service
public class ConfigPropertyService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private HazelcastService cacheService;

    @Autowired
    private CommonService commonService;

    private ConfigProperty configPropertyById(String configPropertyId) {
        ConfigProperty configProperty = null;
        try {
            configProperty = (ConfigProperty) dataAccessService.read(ConfigProperty.class, configPropertyId);
        } catch (Exception e) {
            LOG.error("Error while trying to get system configuration by id");
        }
        return configProperty;
    }

    public List<ConfigPropertyDTO> getAllConfigProperty() {
        List<ConfigProperty> configProperties = null;
        try {
            configProperties = (List<ConfigProperty>) dataAccessService.read(ConfigProperty.class);
        } catch (Exception e) {
            LOG.error("Error while trying to get all system configuration");
        }
        List<ConfigPropertyDTO> cpDTO = ConfigPropertyDTO.getConfigPropertyList(configProperties);
        return cpDTO;
    }

    public ConfigPropertyDTO getConfigPropertyById(String configPropertyId) {
        ConfigPropertyDTO scDTO = new ConfigPropertyDTO(this.configPropertyById(configPropertyId));
        return scDTO;
    }

    public ConfigPropertyDTO updateConfigProperty(ConfigPropertyRequest configPropertyRequest, String configPropertyId) throws Exception {
        ConfigProperty configProperty;

        if (!(ActiontecConstants.DROP_DOWN_TIME_RANGE_COMMON.equals(configPropertyRequest.getProperty()) || ActiontecConstants.DROP_DOWN_TIME_RANGE_STEERING.equals(configPropertyRequest.getProperty()) || ApplicationCommonConstants.DEVICE_RSSI_LIMIT.equals(configPropertyRequest.getProperty()) || ABS_DATABASE.equals(configPropertyRequest.getProperty()) || REALTIME_CONNECTIVITY_STATUS.equals(configPropertyRequest.getProperty()))) {
            if (ValidationUtil.isNotValidIntegerInput(configPropertyRequest.getValue())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide a numeric value");
            }
        }


        if (Objects.nonNull(configPropertyRequest.getProperty()) && ActiontecConstants.DROP_DOWN_TIME_RANGE_COMMON.equals(configPropertyRequest.getProperty())) {
            checkDropDownTimeRange(configPropertyRequest);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && ActiontecConstants.DROP_DOWN_TIME_RANGE_STEERING.equals(configPropertyRequest.getProperty())) {
            checkDropDownTimeRange(configPropertyRequest);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && ABS_DATABASE.equals(configPropertyRequest.getProperty())) {
            checkDBValue(configPropertyRequest);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && ActiontecConstants.DIAGNOSTIC_COLLECTION_INTERVAL_SECONDS.equals(configPropertyRequest.getProperty())) {
            HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
            Integer reportingInterval = Integer.valueOf(equipmentProps.get(DIAGNOSTIC_REPORTING_INTERVAL_SECONDS));
            Integer colInterval = Integer.valueOf(configPropertyRequest.getValue().trim());
            checkDurationRange(configPropertyRequest, (reportingInterval < 300) ? reportingInterval : 300, 10);
            if (reportingInterval % colInterval != 0) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide a divisor of " + reportingInterval);
            }

        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && ActiontecConstants.DIAGNOSTIC_DURATION_SECONDS.equals(configPropertyRequest.getProperty())) {
            HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
            Integer reportingInterval = Integer.valueOf(equipmentProps.get(DIAGNOSTIC_REPORTING_INTERVAL_SECONDS));
            checkDurationRange(configPropertyRequest, 3600, (reportingInterval < 60) ? 60 : reportingInterval);

        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && DIAGNOSTIC_REPORTING_INTERVAL_SECONDS.equals(configPropertyRequest.getProperty())) {
            HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
            Integer colInterval = Integer.valueOf(equipmentProps.get(DIAGNOSTIC_COLLECTION_INTERVAL_SECONDS));
            Integer bustModeDuration = Integer.valueOf(equipmentProps.get(DIAGNOSTIC_DURATION_SECONDS));
            Integer duration = Integer.valueOf(configPropertyRequest.getValue().trim());
            checkDurationRange(configPropertyRequest, (bustModeDuration < 900) ? bustModeDuration : 900, colInterval);
            if (duration % 2 != 0 || duration % colInterval != 0) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide a even integer multiple of " + colInterval);
            }
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && ActiontecConstants.SPEED_TEST_SAMPLE.equals(configPropertyRequest.getProperty())) {
            checkDurationRange(configPropertyRequest, 500, 1);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && ActiontecConstants.CONNECTIVITY_REPORT_LOAD_RATE_MINUTES.equals(configPropertyRequest.getProperty())) {
            checkDurationRange(configPropertyRequest, 1440, 5);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && EVENT_RGW_DISCONNECTED.equals(configPropertyRequest.getProperty())) {
            checkDurationRange(configPropertyRequest, 100, 1);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && TCP_CONNECTION_ESTABLISHED.equals(configPropertyRequest.getProperty())) {
            checkDurationRange(configPropertyRequest, 100, 1);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && VMQ_DISK_PERCENTAGE_USED.equals(configPropertyRequest.getProperty())) {
            checkDurationRange(configPropertyRequest, 100, 1);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && VMQ_MEMORY_PERCENTAGE_USED.equals(configPropertyRequest.getProperty())) {
            checkDurationRange(configPropertyRequest, 100, 1);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && ETL_DEBUG_MINUTES.equals(configPropertyRequest.getProperty())) {
            checkDurationRange(configPropertyRequest, 1440, 1);
        } else if (Objects.nonNull(configPropertyRequest.getProperty()) && REALTIME_CONNECTIVITY_STATUS.equals(configPropertyRequest.getProperty())) {
            if (!configPropertyRequest.getValue().equals("true") && !configPropertyRequest.getValue().equals("false")) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide either 'true' or 'false'.");
            }
        }

        configProperty = ConfigProperty.mapToConfigProperty(configPropertyRequest, this.configPropertyById(configPropertyId));
        if (Objects.nonNull(configProperty)) {
            dataAccessService.update(ConfigProperty.class, configProperty);
            cacheService.delete(SYSTEM_CONFIG_KEY, SYSTEM_CONFIG);
        } else {
            LOG.error("Record does not exists");
        }
        ConfigPropertyDTO cpDTO = new ConfigPropertyDTO(configProperty);
        return cpDTO;
    }

    private void checkDBValue(ConfigPropertyRequest configPropertyRequest) {
        if (Objects.nonNull(configPropertyRequest.getValue())) {
            if (!((configPropertyRequest.getValue().equalsIgnoreCase(DBToFetchSteeringLogs.MONGO.name())) || configPropertyRequest.getValue().equalsIgnoreCase(DBToFetchSteeringLogs.CASSANDRA.name()))) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Value. Allowed Values are MONGO or CASSANDRA");
            }
        }
    }

    private void checkDurationRange(ConfigPropertyRequest configPropertyRequest, Integer max, Integer min) {
        {
            if (Objects.nonNull(configPropertyRequest.getValue())) {
                String duration = configPropertyRequest.getValue().trim();
                if (Integer.valueOf(duration) > max || Integer.valueOf(duration) < min) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide value between " + min + " to " + max + " .");
                }
            }
        }
    }

    private void checkDropDownTimeRange(ConfigPropertyRequest configPropertyRequest) {
        {
            if (Objects.nonNull(configPropertyRequest.getValue())) {
                String[] list = configPropertyRequest.getValue().trim().split(COMMA);
                if (list.length < 1) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide comma separated value. Example : 1h,5h,1d\n h:hour,d=day");
                } else {
                    for (String range : list) {
                        if (ValidationUtil.isHourValid(range)) {
                            continue;
                        } else if (ValidationUtil.isDayValid(range)) {
                            continue;
                        } else {
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Value for this config can lie between 1h and 18d");
                        }
                    }
                }
            }
        }
    }

}
