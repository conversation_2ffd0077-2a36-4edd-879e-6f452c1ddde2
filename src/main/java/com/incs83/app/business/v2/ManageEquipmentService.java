package com.incs83.app.business.v2;

import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.common.v2.AgentVersion;
import com.incs83.app.common.v2.EquipmentFriendlyNameRequest;
import com.incs83.app.common.v2.RadioRequestDTO;
import com.incs83.app.common.v3.NeighborScanRequestDTO;
import com.incs83.app.constants.misc.*;
import com.incs83.app.constants.queries.ClusterInfoSQL;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.cassandra.*;
import com.incs83.app.enums.BandType;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.Equipment.*;
import com.incs83.app.responsedto.v2.RadioDFSResponse;
import com.incs83.app.responsedto.v2.RadioResponseDTO;
import com.incs83.app.service.components.HttpServiceImpl;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.service.repository.CassandraRepository;
import com.incs83.app.utils.EquipmentUtils;
import com.incs83.business.ESService;
import com.incs83.context.ExecutionContext;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.EquipmentSearchDTO;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.pubsub.MQTTService;
import com.incs83.service.CommonService;
import com.incs83.service.S3Service;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.collections4.map.LinkedMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletRequest;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;
import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;
import static com.incs83.app.constants.misc.ApplicationConstants.EXTENDER;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.TWO_DECIMAL_PLACE;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.*;

/**
 * Created by Jayant on 29/1/18.
 */
@Service
public class ManageEquipmentService {

    private final Logger LOG = LogManager.getLogger(this.getClass());
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    CassandraRepository cassandraRepository;
    @Autowired
    private RPCUtilityService rpcUtilityService;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private ESService esService;
    @Autowired
    private ManageSubscriberNetworkStatsService manageSubscriberNetworkStatsService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private SimpleRpcService cpeRpcService;
    @Autowired
    private EquipmentUtils equipmentUtils;
    @Autowired
    private MQTTService mqttService;
    @Autowired
    private HttpServiceImpl httpService;

    private final String UPLOAD_PATH = "UploadedS3ImagesPath";

    @Autowired
    private S3Service s3Service;

    @Value("${vmqApiKey:''}")
    private String vmqApiKey;

    private List<Map<String, Object>> generateTimeSeriesDataForUser(Equipment userEquipment, String serialNumber, long noOfRecord) {
        List<Map<String, Object>> timeSeriesData = new ArrayList<>();

        List<Map<String, Object>> data = getConvertedData(userEquipment, serialNumber, noOfRecord);
        if (Objects.nonNull(data) && !data.isEmpty()) {
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (noOfRecord * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            data = data.stream().filter(element -> Objects.nonNull(element.get("timestamp")) && (Long.valueOf(element.get("timestamp").toString()) > currTimeStamp)).collect(Collectors.toList());
            if (Objects.nonNull(data) && !data.isEmpty()) {
                timeSeriesData.addAll(data);
            }
        }
        timeSeriesData.sort(Comparator.comparing(s -> Long.valueOf(s.get("timestamp").toString())));
        return timeSeriesData;
    }


    public List<Map<String, Object>> getConvertedData(Equipment userEquipment, String serialNumber, long noOfRecord) {
        List<Map<String, Object>> timeSeriesData = new ArrayList<>();

        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        long minutes = (noOfRecord < 60) ? HOURLY_DATA : noOfRecord;

//        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", serialNumber);
        params.put("dateHour", dateCriteria);

        List<DBObject> dbObjectList = mongoService.findList(params, null, AP_WIFI_INSIGHTS_PER_MINUTE, mongoFieldOptions);
        if (!dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                if (Objects.nonNull(dbObject.get("hourlyData"))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get("hourlyData");
                    for (String key : dataByDay.keySet()) {
                        List<Map<String, Object>> slidingDataMap = (List<Map<String, Object>>) dataByDay.get(key);
                        if (!slidingDataMap.isEmpty()) {
                            timeSeriesData.addAll(slidingDataMap);
                        }
                    }
                }
            }

        }
        return timeSeriesData;
    }


    public HashMap<String, Object> getAllEquipmentsForSubscriber(String equipmentIdOrSerialOrSTN) throws
            Exception {
        HashMap<String, Object> response = new HashMap<>();
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userEquipment.getRgwSerial());

        BasicDBObject mongoFieldOptions = new BasicDBObject();

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        List<BasicDBObject> equipmentList = mongoService.findList(params, AP_DETAIL, mongoFieldOptions);
        equipmentList = manageCommonService.filterEquipmentsAndCheckForRgwAndExt(equipmentList);

        //EquipmentListDTO equipmentListDTO = new EquipmentListDTO();
        ArrayList<EquipmentListDTO.EquipmentListData> equipmentListData = new ArrayList<>();
        Boolean isEnabled = false;
        for (BasicDBObject equipmentObject : equipmentList) {
            EquipmentListDTO.EquipmentListData equipmentData = new EquipmentListDTO.EquipmentListData();
            equipmentData.setName(manageCommonService.getDisplayNameForEquipment(equipmentObject));
            equipmentData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull((equipmentObject.get("macAddress"))) ? null : (equipmentObject.get("macAddress")).toString());
            equipmentData.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipmentObject));
            equipmentData.setSerialNumber(Objects.isNull((equipmentObject.get("serialNumber"))) ? null : (equipmentObject.get("serialNumber")).toString());
            equipmentData.setType(Objects.isNull((equipmentObject.get("type"))) ? null : (equipmentObject.get("type")).toString());
            equipmentData.setBuildVersion(Objects.nonNull(equipmentObject.get("buildVersion")) ? equipmentObject.get("buildVersion").toString() : "N/A");
            equipmentData.setFwVersion(Objects.nonNull(equipmentObject.get("fwVersion")) ? equipmentObject.get("fwVersion").toString() : "N/A");
            equipmentData.setSeverity(calculateSeverityForEquipment(/*getDeviceAlarmList(deviceDetailList, equipmentData.getSerialNumber()),*/ getEquipmentAlarmList(equipmentObject, userEquipment)));
            equipmentData.setIsp(Objects.isNull(equipmentObject.get("isp")) ? "N/A" : String.valueOf(equipmentObject.get("isp")));
            equipmentData.setEtlVersion(String.valueOf(equipmentObject.get("etlVersion")));
            equipmentData.setDiagnosticsEnabled(Objects.isNull(equipmentObject.get("diagnosEnabled")) ? false : (Boolean) equipmentObject.get("diagnosEnabled"));
            equipmentListData.add(equipmentData);
            isEnabled = equipmentData.getDiagnosticsEnabled() ? true : isEnabled;
        }

        if (equipmentListData.isEmpty()) {
            EquipmentListDTO.EquipmentListData equipmentData = new EquipmentListDTO.EquipmentListData();
            equipmentListData.add(equipmentData);
        } else {
            equipmentListData.sort(Comparator.comparing(EquipmentListDTO.EquipmentListData::getType));
            Collections.reverse(equipmentListData);
        }
        response.put("equipmentDetails", equipmentListData);
        response.put("diagnosticsEnabled", isEnabled);
        return response;
    }

    private int calculateSeverityForEquipment
            (/*ArrayList<DeviceDetail> deviceDetailList, */List<HashMap<String, Object>> alarmListForEquipment) {
        int alarmLevelSeverity = -1;
        for (HashMap<String, Object> i : alarmListForEquipment) {
            if (Objects.nonNull(i.get("severity"))) {
                if (Integer.parseInt(i.get("severity").toString()) > alarmLevelSeverity) {
                    alarmLevelSeverity = Integer.parseInt(i.get("severity").toString());
                }
            }
        }
        return alarmLevelSeverity;
    }

    private int getRebootCount(BasicDBObject equipment) {
        String serial = equipment.get("serialNumber").toString() != null ? equipment.get("serialNumber").toString() : null;
        int count = 0;

        if(serial != null) {
            ZonedDateTime ldtNow = ZonedDateTime.now();

            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.put("_id", 0);

            HashMap<String, Object> query = new HashMap<>();
            query.put("serialNumber", serial);
            query.put("uptime", new BasicDBObject("$gte", ldtNow.minusDays(LOCALDATETIME_CRITERIA_LAST_7_DAYS).toEpochSecond()));

            count = mongoService.findList(query, REBOOT_HISTORY, mongoFieldOptions).size();
        }

        return count;
    }

    public List<HashMap<String, Object>> getEquipmentAlarmList(BasicDBObject equipment, Equipment userEquipment) {
        //// ALARMS FOR EQUIPMENT START
        List<HashMap<String, Object>> alarmListForEquipment = new ArrayList<>();
        HashMap<String, Object> oneWifiBandAlarm = new HashMap<>();
        HashMap<String, Object> allWifiBandAlarm = new HashMap<>();
        HashMap<String, Object> lowMemoryAlarm = new HashMap<>();
        HashMap<String, Object> notReportingAlarm = new HashMap<>();
        HashMap<String, Object> downLinkBandWidthUsageAlarm = new HashMap<>();
        HashMap<String, Object> upLinkBandWidthUsageAlarm = new HashMap<>();
        HashMap<String, Object> rebootAlarm = new HashMap<>();
        BasicDBList radioList = (BasicDBList) equipment.get("wifiRadios");
        String model = String.valueOf(equipment.get("modelName"));

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_CONFIG);
        Double radioOccupancyHigh = 80.0;
        Double radioOccupancyLow = 60.0;
        try {
            radioOccupancyHigh = Double.valueOf(equipmentProps.get(EQUIPMENT_RADIO_OCCUPANCY_ALARM_THRESHOLD));
            radioOccupancyLow = Double.valueOf(equipmentProps.get(EQUIPMENT_RADIO_OCCUPANCY_ALERT_THRESHOLD));
        } catch (Exception e) {

        }

        Double memUsageHigh = 90.0;
        Double memUsageLow = 80.0;
        try {
            memUsageHigh = Double.valueOf(equipmentProps.get(EQUIPMENT_MEMORY_USAGE_ALARM_THRESHOLD));
            memUsageLow = Double.valueOf(equipmentProps.get(EQUIPMENT_MEMORY_USAGE_ALERT_THRESHOLD));
        } catch (Exception e) {

        }

        Double utilizationHigh = 80.00;
        Double utilizationLow = 60.00;
        try {
            utilizationHigh = Double.valueOf(String.valueOf(equipmentProps.get(EQUIPMENT_INTERNET_UTILIZATION_USAGE_ALARM_THRESHOLD)));
            utilizationLow = Double.valueOf(String.valueOf(equipmentProps.get(EQUIPMENT_INTERNET_UTILIZATION_USAGE_ALERT_THRESHOLD)));
        } catch (Exception e) {

        }
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        int radioFalseCount = 0;
        for(Object radio : radioList) {
            int enabled = Integer.valueOf(((BasicDBObject) radio).get("enable").toString());
            if(enabled == 0) {
                radioFalseCount++;
            }
        }

        if(radioFalseCount == radioList.size()) {
            allWifiBandAlarm.put("alarmType", "equipmentAlarm");
            allWifiBandAlarm.put("mac", null);
            allWifiBandAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            allWifiBandAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            allWifiBandAlarm.put("severity", 1);
            allWifiBandAlarm.put("name",  manageCommonService.getDisplayNameForEquipment(equipment));
            allWifiBandAlarm.put("desc", "is operating with all WiFi bands out of service.");
            alarmListForEquipment.add(allWifiBandAlarm);
        } else if(radioFalseCount != 0 && radioFalseCount < radioList.size()) {
            oneWifiBandAlarm.put("alarmType", "equipmentAlarm");
            oneWifiBandAlarm.put("mac", null);
            oneWifiBandAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            oneWifiBandAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            oneWifiBandAlarm.put("severity", 0);
            oneWifiBandAlarm.put("name",  manageCommonService.getDisplayNameForEquipment(equipment));
            oneWifiBandAlarm.put("desc", "is operating with one or more WiFi bands out of service.");
            alarmListForEquipment.add(oneWifiBandAlarm);
        }

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        String serialNumber = Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null;
        List<Map<String, Object>> radioOccInfo = generateTimeSeriesDataForUser(userEquipment, serialNumber, 60);

        if (!radioOccInfo.isEmpty()) {
            Map<String, ArrayList<Double>> radioCountMap = new HashMap<>();
            for(Map<String, Object> radioInfo : radioOccInfo) {
                Map<String, Object> radioElement = (Map<String, Object>) radioInfo.get("radio");
                List<String> radioKeyList = radioElement.keySet().stream().collect(Collectors.toList());

                for(String radioKey : radioKeyList) {
                    Map<String, Object> radioMap = (Map<String, Object>) radioElement.get(radioKey);
                    if(radioCountMap.get(radioKey) == null) {
                        radioCountMap.put(radioKey, new ArrayList<>());
                    }
                    ArrayList<Double> busyList = radioCountMap.get(radioKey);
                    busyList.add(Double.valueOf(radioMap.get("occupancy").toString()));
                }
            }

            if(!model.equals("F5380") && !model.equals("Fast399")) {
                List<String> radioKeyList = radioCountMap.keySet().stream().collect(Collectors.toList());
                for (String radioKey : radioKeyList) {
                    ArrayList<Double> busyList = radioCountMap.get(radioKey);
                    Double busy = busyList.stream().mapToDouble(val -> val).average().orElse(0.0);
                    busy = Double.valueOf(TWO_DECIMAL_PLACE.format(busy));

                    if (busy >= radioOccupancyLow && busy < radioOccupancyHigh) {
                        HashMap<String, Object> radioOccupancyAlarm = new HashMap<>();
                        radioOccupancyAlarm.put("alarmType", "equipmentAlarm");
                        radioOccupancyAlarm.put("mac", null);
                        radioOccupancyAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                        radioOccupancyAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                        radioOccupancyAlarm.put("severity", 0);
                        radioOccupancyAlarm.put("name", manageCommonService.getDisplayNameForEquipment(equipment));
                        radioOccupancyAlarm.put("desc", RadioEnum.getRadioKeyByAlias(radioKey) + " WiFi average radio occupancy for last 1 hour is " + busy + "%. Review the Equipment Details and WiFi Details sections to identify the devices that are using the most WiFi bandwidth.");
                        alarmListForEquipment.add(radioOccupancyAlarm);
                    } else if (busy >= radioOccupancyHigh) {
                        HashMap<String, Object> radioOccupancyAlarm = new HashMap<>();
                        radioOccupancyAlarm.put("alarmType", "equipmentAlarm");
                        radioOccupancyAlarm.put("mac", null);
                        radioOccupancyAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                        radioOccupancyAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                        radioOccupancyAlarm.put("severity", 1);
                        radioOccupancyAlarm.put("name", manageCommonService.getDisplayNameForEquipment(equipment));
                        radioOccupancyAlarm.put("desc", RadioEnum.getRadioKeyByAlias(radioKey) + " WiFi average radio occupancy for last 1 hour is " + busy + "%. Review the Equipment Details and WiFi Details sections to identify the devices that are using the most WiFi bandwidth.");
                        alarmListForEquipment.add(radioOccupancyAlarm);
                    }
                }
            }
        }


        Double memUsage = Objects.nonNull(equipment.get("memUsage")) ? Double.valueOf(equipment.get("memUsage").toString()) : 0.0;
        if (memUsage >= memUsageLow && memUsage < memUsageHigh) {
            lowMemoryAlarm.put("alarmType", "equipmentAlarm");
            lowMemoryAlarm.put("mac", null);
            lowMemoryAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            lowMemoryAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            lowMemoryAlarm.put("severity", 0);
            lowMemoryAlarm.put("name",  manageCommonService.getDisplayNameForEquipment(equipment));
            lowMemoryAlarm.put("desc", "is running low on memory. Power cycle the equipment or click REBOOT when convenient.");
            alarmListForEquipment.add(lowMemoryAlarm);
        } else if (memUsage >= memUsageHigh) {
            lowMemoryAlarm.put("alarmType", "equipmentAlarm");
            lowMemoryAlarm.put("mac", null);
            lowMemoryAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            lowMemoryAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            lowMemoryAlarm.put("severity", 1);
            lowMemoryAlarm.put("name",  manageCommonService.getDisplayNameForEquipment(equipment));
            lowMemoryAlarm.put("desc", "is running low on memory. Power cycle the equipment or click REBOOT when convenient.");
            alarmListForEquipment.add(lowMemoryAlarm);
        }

        if (Objects.nonNull(equipment.get("type")) && equipment.get("type").toString().equals(GATEWAY)) {
            Map<String, Double> internetUtilizationMap = manageSubscriberNetworkStatsService.getEquipAvgInterNetUtilizationForDuration(60, userEquipment);
            double downLoadAvgInternetUtilizationFor30Mins = Objects.nonNull(internetUtilizationMap)? internetUtilizationMap.get("downLoadAvg"): 0.0;
            downLoadAvgInternetUtilizationFor30Mins = (downLoadAvgInternetUtilizationFor30Mins * 8) / (1000F * 1000F); // Converting to Mbps
            double subscriberDownLinkBandwidth = userEquipment.getDownLinkRate();
            if (subscriberDownLinkBandwidth == 0)
                subscriberDownLinkBandwidth = 1;
            double downLinkPercentageUtilization = (Double.parseDouble(TWO_DECIMAL_PLACE.format((downLoadAvgInternetUtilizationFor30Mins * 100 / subscriberDownLinkBandwidth))));
            if (downLinkPercentageUtilization >= utilizationLow && downLinkPercentageUtilization < utilizationHigh) {
                downLinkBandWidthUsageAlarm.put("alarmType", "equipmentAlarm");
                downLinkBandWidthUsageAlarm.put("mac", null);
                downLinkBandWidthUsageAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                downLinkBandWidthUsageAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                downLinkBandWidthUsageAlarm.put("severity", 0);
                downLinkBandWidthUsageAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                downLinkBandWidthUsageAlarm.put("desc", "Your Current Downlink Internet Usage is approaching the maximum available bandwidth. Review the Internet Details section to identify the devices that are consuming the most downlink traffic.");
                alarmListForEquipment.add(downLinkBandWidthUsageAlarm);
            } else if (downLinkPercentageUtilization >= utilizationHigh) {
                downLinkBandWidthUsageAlarm.put("alarmType", "equipmentAlarm");
                downLinkBandWidthUsageAlarm.put("mac", null);
                downLinkBandWidthUsageAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                downLinkBandWidthUsageAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                downLinkBandWidthUsageAlarm.put("severity", 1);
                downLinkBandWidthUsageAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                downLinkBandWidthUsageAlarm.put("desc", "Your Current Downlink Internet Usage is approaching the maximum available bandwidth. Review the Internet Details section to identify the devices that are consuming the most downlink traffic.");
                alarmListForEquipment.add(downLinkBandWidthUsageAlarm);
            }

            //For UpLink Alerts
            double subscriberUpLinkBandwidth = userEquipment.getUpLinkRate();
            if (subscriberUpLinkBandwidth == 0)
                subscriberUpLinkBandwidth = 1;
            double upLoadAvgInternetUtilizationFor30Mins = Objects.nonNull(internetUtilizationMap)? internetUtilizationMap.get("upLoadAvg"): 0.0;
            upLoadAvgInternetUtilizationFor30Mins = (upLoadAvgInternetUtilizationFor30Mins * 8) / (1000F * 1000F); // Converting to Mbps
            double upLinkPercentageUtilization = (Double.parseDouble(TWO_DECIMAL_PLACE.format((upLoadAvgInternetUtilizationFor30Mins * 100 / subscriberUpLinkBandwidth))));
            if (upLinkPercentageUtilization >= utilizationLow && upLinkPercentageUtilization < utilizationHigh) {
                upLinkBandWidthUsageAlarm.put("alarmType", "equipmentAlarm");
                upLinkBandWidthUsageAlarm.put("mac", null);
                upLinkBandWidthUsageAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                upLinkBandWidthUsageAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                upLinkBandWidthUsageAlarm.put("severity", 0);
                upLinkBandWidthUsageAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                upLinkBandWidthUsageAlarm.put("desc", "Your Current Uplink Internet Usage is approaching the maximum available bandwidth. Review the Internet Details section to identify the devices that are consuming the most uplink traffic.");
                alarmListForEquipment.add(upLinkBandWidthUsageAlarm);
            } else if (upLinkPercentageUtilization >= utilizationHigh) {
                upLinkBandWidthUsageAlarm.put("alarmType", "equipmentAlarm");
                upLinkBandWidthUsageAlarm.put("mac", null);
                upLinkBandWidthUsageAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                upLinkBandWidthUsageAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                upLinkBandWidthUsageAlarm.put("severity", 1);
                upLinkBandWidthUsageAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                upLinkBandWidthUsageAlarm.put("desc", "Your Current Uplink Internet Usage is approaching the maximum available bandwidth. Review the Internet Details section to identify the devices that are consuming the most uplink traffic.");
                alarmListForEquipment.add(upLinkBandWidthUsageAlarm);
            }
        }
        if (manageCommonService.getConnectivityStatusForEquipment(equipment).equals("RED")) {
            notReportingAlarm.put("alarmType", "equipmentAlarm");
            notReportingAlarm.put("mac", null);
            notReportingAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            notReportingAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            notReportingAlarm.put("severity", 1);
            notReportingAlarm.put("name",  manageCommonService.getDisplayNameForEquipment(equipment));
            notReportingAlarm.put("desc", "is not reporting to Optim. Check that all cables are plugged in correctly, confirm that the " + notReportingAlarm.get("name") + "’s lights are green and then power cycle if necessary.");
            alarmListForEquipment.add(notReportingAlarm);
        }

//        Integer rebootThreshold = Integer.valueOf(equipmentProps.get(EQUIPMENT_REBOOT_ALERT_THRESHOLD));
//        int rebootCount = getRebootCount(equipment);
//        if(rebootCount >= rebootThreshold) {
//            oneWifiBandAlarm.put("alarmType", "equipmentAlarm");
//            oneWifiBandAlarm.put("mac", null);
//            oneWifiBandAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
//            oneWifiBandAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
//            oneWifiBandAlarm.put("severity", 1);
//            oneWifiBandAlarm.put("name",  manageCommonService.getDisplayNameForEquipment(equipment));
//            oneWifiBandAlarm.put("desc", "has rebooted more than " + rebootThreshold + " times in the last 7 days.");
//            alarmListForEquipment.add(oneWifiBandAlarm);
//        }
        return alarmListForEquipment;
    }

    public ApiResponseDTO getEquipmentDetails(String equipmentIdOrSerialOrSTN, String serialNumber, String
            infoType) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("serialNumber", serialNumber);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment with SerialNumber : " + serialNumber + " does not belong to Subscriber with Id / RGW Serial: " + userEquipment.getRgwSerial() + " / " + userEquipment.getRgwSerial());
        }

        EquipmentDetailsDTO equipmentDetailsDTO = new EquipmentDetailsDTO();
        EquipmentDetailsDTO.EquipmentDetailData equipmentDetailData = equipmentDetailsDTO.new EquipmentDetailData();

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        if (infoType.equals(system) || infoType.equals(all)) {
            EquipmentSystemDTO equipmentSystemDTO = new EquipmentSystemDTO();
            EquipmentSystemDTO.EquipmentSystemData equipmentSystemData = equipmentSystemDTO.new EquipmentSystemData();

            Calendar now = Calendar.getInstance();
            long nowMillSec = now.getTimeInMillis();

            equipmentSystemData.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(aPDetails));
            equipmentSystemData.setModelName(Objects.isNull(aPDetails.get("modelName")) ? null : aPDetails.get("modelName").toString());
            equipmentSystemData.setFwVersion(Objects.isNull(aPDetails.get("fwVersion")) ? null : aPDetails.get("fwVersion").toString());
            equipmentSystemData.setSerialNumber(Objects.isNull(aPDetails.get("serialNumber")) ? null : aPDetails.get("serialNumber").toString());
            equipmentSystemData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(aPDetails.get("macAddress")) ? null : aPDetails.get("macAddress").toString());
            equipmentSystemData.setLastReboot(Objects.isNull(aPDetails.get("uptime")) ? nowMillSec : (Long.parseLong(aPDetails.get("uptime").toString()) * 1000));
            if (manageCommonService.getConnectivityStatusForEquipment(aPDetails).equals("RED")) {
                equipmentSystemData.setUptime(null);
                equipmentSystemData.setMemoryUsage(null);
                equipmentSystemData.setCpuUsage(null);
            } else {
                equipmentSystemData.setUptime(CommonUtils.getHistoricalTimeFromDate(Long.parseLong(Objects.isNull(aPDetails.get("uptime")) ? "0" : aPDetails.get("uptime").toString()) * 1000));
                equipmentSystemData.setMemoryUsage(Objects.isNull(aPDetails.get("memUsage")) ? null : Double.valueOf(aPDetails.get("memUsage").toString()));
                equipmentSystemData.setCpuUsage(Objects.isNull(aPDetails.get("cpuUsage")) ? null : Double.valueOf(aPDetails.get("cpuUsage").toString()));
            }

            equipmentSystemData.setHealth(Objects.isNull(aPDetails.get("health")) ? null : aPDetails.get("health"));
            equipmentSystemData.setEquipmentType(Objects.isNull(aPDetails.get("type")) ? null : aPDetails.get("type").toString());
            equipmentSystemData.setLastReported(Objects.isNull(aPDetails.get("timestamp")) ? 0 : Long.valueOf(aPDetails.get("timestamp").toString()));

            equipmentSystemData.setNtInfoTimestamp(Objects.isNull(aPDetails.get("ntInfoTimestamp")) ? 0 : Long.valueOf(aPDetails.get("ntInfoTimestamp").toString()));
            equipmentSystemData.setNtReportTimestamp(Objects.isNull(aPDetails.get("ntReportTimestamp")) ? 0 : Long.valueOf(aPDetails.get("ntReportTimestamp").toString()));
            equipmentSystemData.setDbInfoTimestamp(Objects.isNull(aPDetails.get("dbInfoTimestamp")) ? 0 : Long.valueOf(aPDetails.get("dbInfoTimestamp").toString()));
            equipmentSystemData.setDbReportTimestamp(Objects.isNull(aPDetails.get("dbReportTimestamp")) ? 0 : Long.valueOf(aPDetails.get("dbReportTimestamp").toString()));
            equipmentSystemData.setDbReportTimestamp(Objects.isNull(aPDetails.get("dbReportTimestamp")) ? 0 : Long.valueOf(aPDetails.get("dbReportTimestamp").toString()));
            equipmentSystemData.setLocalTimestamp(Objects.isNull(aPDetails.get("localTimestamp")) ? 0 : Long.valueOf(aPDetails.get("localTimestamp").toString()));

            equipmentSystemData.setSmartSteering(manageCommonService.isSmartSteeringEnabledForDeviceMac(userEquipment.getRgwSerial(), serialNumber));
            equipmentSystemData.setBuildVersion(Objects.nonNull(aPDetails.get("buildVersion")) ? aPDetails.get("buildVersion").toString() : "N/A");
            equipmentSystemData.setIsp(Objects.nonNull(aPDetails.get("isp")) ? aPDetails.get("isp").toString() : "N/A");
            String friendlyName = Objects.isNull(aPDetails.get("friendlyName")) ? Objects.isNull(aPDetails.get("modelName")) ? aPDetails.get("macAddress").toString() : aPDetails.get("modelName").toString() : aPDetails.get("friendlyName").toString();

            equipmentSystemData.setFriendlyName(friendlyName);
            List<HashMap<String, Object>> alarmListForEquipment = getEquipmentAlarmList((BasicDBObject) aPDetails, userEquipment);

            equipmentSystemData.setAlarms(alarmListForEquipment);
            equipmentSystemData.setSeverity(calculateSeverityForEquipment(alarmListForEquipment));
            equipmentSystemDTO.setData(equipmentSystemData);
            if (infoType.equals(system))
                return equipmentSystemDTO;
            else
                equipmentDetailData.setSystem(equipmentSystemData);
        }

        Map<String, Object> txRxBytes = manageCommonService.getTimeSeriesDataForWifiHealthPerMinute(userEquipment, String.valueOf(aPDetails.get("serialNumber")));

        if (infoType.equals(wan) || infoType.equals(all)) {
            EquipmentWANDTO equipmentWANDTO = new EquipmentWANDTO();
            EquipmentWANDTO.EquipmentWANData equipmentWANData = equipmentWANDTO.new EquipmentWANData();

            DBObject wanDetails = (DBObject) aPDetails.get("wan");

            if (wanDetails != null) {
                equipmentWANData.setDevIpAddress(Objects.isNull(wanDetails.get("devIpAddress")) ? null : wanDetails.get("devIpAddress").toString());
                equipmentWANData.setStatus((wanDetails.get("status") != null && "1".equals(Objects.isNull(wanDetails.get("status")) ? null : wanDetails.get("status").toString())) ? "Up" : "Down");
                equipmentWANData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.parseDouble(Objects.isNull(txRxBytes.get("wanRxBytes")) ? "0.0" : txRxBytes.get("wanRxBytes").toString()));
                equipmentWANData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.parseDouble(Objects.isNull(txRxBytes.get("wanTxBytes")) ? "0.0" : txRxBytes.get("wanTxBytes").toString()));
                equipmentWANData.setLeaseTimeRemaining(CommonUtils.getHistoricalTimeFromDate(Calendar.getInstance().getTimeInMillis() + (Long.parseLong(String.valueOf(Objects.isNull(wanDetails.get("leaseTimeRemaining")) ? 0 : wanDetails.get("leaseTimeRemaining"))) * 1000)));
                equipmentWANData.setLeaseTimeRemainingInSeconds(Objects.isNull(wanDetails.get("leaseTimeRemaining")) ? 0 : Integer.valueOf(wanDetails.get("leaseTimeRemaining").toString()));
                equipmentWANData.setGateway(Objects.isNull(wanDetails.get("gateway")) ? null : wanDetails.get("gateway").toString());
                equipmentWANData.setDhcp(Objects.isNull(wanDetails.get("dhcp")) ? null : wanDetails.get("dhcp").toString());
                equipmentWANData.setDns1(Objects.isNull(wanDetails.get("dns1")) ? null : wanDetails.get("dns1").toString());
                equipmentWANData.setDns2(Objects.isNull(wanDetails.get("dns2")) ? null : wanDetails.get("dns2").toString());
                equipmentWANData.setPhyType(Objects.isNull(wanDetails.get("phyType")) ? null : wanDetails.get("phyType").toString());
                equipmentWANData.setPhyTransmitRate(Objects.isNull(wanDetails.get("phyTransmitRate")) ? 0 : Long.parseLong(wanDetails.get("phyTransmitRate").toString()));
                equipmentWANData.setPhyReceiveRate(Objects.isNull(wanDetails.get("phyReceiveRate")) ? 0 : Long.parseLong(wanDetails.get("phyReceiveRate").toString()));
                equipmentWANData.setMacAddress(Objects.isNull(wanDetails.get("macAddress")) ? null : wanDetails.get("macAddress").toString());
                equipmentWANData.setLastChange(Objects.isNull(wanDetails.get("lastChange")) ? 0L : (Long.valueOf(wanDetails.get("lastChange").toString()) * 1000));
                equipmentWANData.setUptime(Objects.isNull(aPDetails.get("uptime")) ? 0L : (Long.valueOf(aPDetails.get("uptime").toString()) * 1000));
                equipmentWANDTO.setData(equipmentWANData);
            }

            if (infoType.equals(wan))
                return equipmentWANDTO;
            else
                equipmentDetailData.setWan(equipmentWANData);

        }

        if (infoType.equals(moca) || infoType.equals(all)) {
            EquipmentMoCADTO equipmentMoCADTO = new EquipmentMoCADTO();
            EquipmentMoCADTO.EquipmentMoCAData equipmentMoCAData = equipmentMoCADTO.new EquipmentMoCAData();

            DBObject mocaDetails = (DBObject) aPDetails.get("moca");

            if (mocaDetails != null) {
                equipmentMoCAData.setMocaStandard(Objects.isNull(mocaDetails.get("mocaStandard")) ? null : mocaDetails.get("mocaStandard").toString());
                equipmentMoCAData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(mocaDetails.get("macAddress")) ? null : mocaDetails.get("macAddress").toString());
                equipmentMoCAData.setStatus("1".equals(Objects.isNull(mocaDetails.get("status")) ? null : mocaDetails.get("status").toString()) ? "Up" : "Down");
                equipmentMoCAData.setLastChange(Long.valueOf(String.valueOf(Objects.isNull(mocaDetails.get("lastChange")) ? 0 : mocaDetails.get("lastChange"))));
                equipmentMoCAData.setUptime(Objects.isNull(mocaDetails.get("uptime")) ? 0 : (Long.parseLong(mocaDetails.get("uptime").toString())) * 1000);
                if (Objects.nonNull(equipmentMoCAData.getMocaStandard()) && (!equipmentMoCAData.getMocaStandard().equals("0.0") || !equipmentMoCAData.getStatus().equals("Down"))) {
                    equipmentMoCAData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.parseDouble(Objects.isNull(txRxBytes.get("mocaRxBytes")) ? "0.0" : String.valueOf(txRxBytes.get("mocaRxBytes"))));
                    equipmentMoCAData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.parseDouble(Objects.isNull(txRxBytes.get("mocaTxBytes")) ? "0.0" : String.valueOf(txRxBytes.get("mocaTxBytes"))));
                } else {
                    equipmentMoCAData.setRxBytes(0.0);
                    equipmentMoCAData.setTxBytes(0.0);
                }
                equipmentMoCADTO.setData(equipmentMoCAData);
            }
            if (infoType.equals(moca))
                return equipmentMoCADTO;
            else
                equipmentDetailData.setMoca(equipmentMoCAData);

        }

        if (infoType.equals(mocaInfo) || infoType.equals(all)) {
            EquipmentMoCALanDTO equipmentMoCALanDTO = new EquipmentMoCALanDTO();
            ArrayList<EquipmentMoCALanDTO.EquipmentMoCALanData> equipmentMoCALanDataList = new ArrayList<>();
            List<DBObject> mocaLanDevices = (List<DBObject>) aPDetails.get("mocaDevices");

            if (mocaLanDevices != null) {
                mocaLanDevices.forEach(item -> {
                    EquipmentMoCALanDTO.EquipmentMoCALanData equipmentMoCALanData = equipmentMoCALanDTO.new EquipmentMoCALanData();
                    equipmentMoCALanData.setTxPower(Objects.isNull(item.get("txPower")) ? 0.0 : Double.valueOf(item.get("txPower").toString()));
                    equipmentMoCALanData.setRxPower(Objects.isNull(item.get("rxPower")) ? 0.0 : Double.valueOf(item.get("rxPower").toString()));
                    equipmentMoCALanData.setTxPHYRate(Objects.isNull(item.get("txPHYRate")) ? 0.0 : Double.valueOf(item.get("txPHYRate").toString()));
                    equipmentMoCALanData.setRxPHYRate(Objects.isNull(item.get("rxPHYRate")) ? 0.0 : Double.valueOf(item.get("rxPHYRate").toString()));
                    equipmentMoCALanData.setAttenuation(Objects.isNull(item.get("attenuation")) ? "0" : item.get("attenuation").toString());
                    equipmentMoCALanData.setHostName(Objects.isNull(item.get("hostName")) ? null : item.get("hostName").toString());
                    equipmentMoCALanData.setIp(Objects.isNull(item.get("ip")) ? null : item.get("ip").toString());
                    equipmentMoCALanData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(item.get("macAddress")) ? null : item.get("macAddress").toString());
                    equipmentMoCALanData.setMode(Objects.isNull(item.get("mode")) ? null : item.get("mode").toString());
                    equipmentMoCALanData.setUptime(Long.valueOf(Objects.isNull(item.get("uptime")) ? "0" : item.get("uptime").toString()));

                    if (Objects.isNull(item.get("upstream")) || (((BasicDBObject) item).getBoolean("upstream") == false)) {
                        equipmentMoCALanDataList.add(equipmentMoCALanData);
                    }
                });
            }
            if (equipmentMoCALanDataList.isEmpty()) {
                EquipmentMoCALanDTO.EquipmentMoCALanData equipmentMoCALanData = equipmentMoCALanDTO.new EquipmentMoCALanData();
                equipmentMoCALanDataList.add(equipmentMoCALanData);
            }
            equipmentMoCALanDTO.setData(equipmentMoCALanDataList);
            if (infoType.equals(mocaInfo))
                return equipmentMoCALanDTO;
            else
                equipmentDetailData.setMocaLan(equipmentMoCALanDataList);

        }

        if (infoType.equals(ethernet) || infoType.equals(all)) {
            EquipmentEthernetDTO equipmentEthernetDTO = new EquipmentEthernetDTO();
            ArrayList<EquipmentEthernetDTO.EquipmentEthernetData> equipmentEthernetDataList = new ArrayList<>();
            List<DBObject> ethernetDetails = (List<DBObject>) aPDetails.get("ethPorts");

            if (!ethernetDetails.isEmpty()) {
                ethernetDetails.removeAll(Collections.singleton(null)); // TO REMOVE NULL VALUES FROM ethernetDetails
                for (int i = 0; i < ethernetDetails.size(); i++) {
                    DBObject item = ethernetDetails.get(i);
                    if (Objects.isNull(item.get("upstream")) || Boolean.valueOf(item.get("upstream").toString()) == false) {
                        EquipmentEthernetDTO.EquipmentEthernetData equipmentEthernetData = equipmentEthernetDTO.new EquipmentEthernetData();
                        equipmentEthernetData.setLastChange((Long.parseLong(Objects.isNull(item.get("lastChange")) ? "0" : item.get("lastChange").toString())) * 1000);
                        equipmentEthernetData.setPort(Objects.isNull(item.get("port")) ? 0 : Long.valueOf(item.get("port").toString()));
                        equipmentEthernetData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.valueOf(Objects.isNull(txRxBytes.get("ethP" + i + "RxBytes")) ? "0" : (txRxBytes.get("ethP" + i + "RxBytes")).toString()));
                        equipmentEthernetData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.valueOf(Objects.isNull(txRxBytes.get("ethP" + i + "TxBytes")) ? "0" : txRxBytes.get("ethP" + i + "TxBytes").toString()));
                        equipmentEthernetData.setStatus(Objects.isNull(item.get("status")) ? null : item.get("status").toString());
                        equipmentEthernetData.setMaxBitRate(Objects.isNull(item.get("maxBitRate")) ? 0 : Integer.valueOf((item.get("maxBitRate").toString())));
                        equipmentEthernetData.setCurrentBitRate(Objects.isNull(item.get("currentBitRate")) ? 0 : Integer.valueOf(item.get("currentBitRate").toString()));
                        equipmentEthernetDataList.add(equipmentEthernetData);
                    }
                }

                if (equipmentEthernetDataList.isEmpty()) {
                    EquipmentEthernetDTO.EquipmentEthernetData equipmentEthernetData = equipmentEthernetDTO.new EquipmentEthernetData();
                    equipmentEthernetDataList.add(equipmentEthernetData);
                } else {
                    double totalRx = Objects.isNull(txRxBytes) ? 0.0 : Double.valueOf(Objects.isNull(txRxBytes.get("ethP0RxBytes")) ? "0.0" : txRxBytes.get("ethP0RxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP1RxBytes")) ? "0.0" : txRxBytes.get("ethP1RxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP2RxBytes")) ? "0.0" : txRxBytes.get("ethP2RxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP3RxBytes")) ? "0.0" : txRxBytes.get("ethP3RxBytes").toString());
                    double totalTx = Objects.isNull(txRxBytes) ? 0.0 : Double.valueOf(Objects.isNull(txRxBytes.get("ethP0TxBytes")) ? "0.0" : txRxBytes.get("ethP0TxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP1TxBytes")) ? "0.0" : txRxBytes.get("ethP1TxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP2TxBytes")) ? "0.0" : txRxBytes.get("ethP2TxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP3TxBytes")) ? "0.0" : txRxBytes.get("ethP3TxBytes").toString());

                    EquipmentEthernetDTO.EquipmentEthernetData equipmentEthernetData = equipmentEthernetDTO.new EquipmentEthernetData();
                    equipmentEthernetData.setLastChange(0L);
                    equipmentEthernetData.setPort(0L);
                    equipmentEthernetData.setRxBytes(totalRx);
                    equipmentEthernetData.setTxBytes(totalTx);
                    equipmentEthernetData.setStatus(null);
                    equipmentEthernetDataList.add(equipmentEthernetData);
                }

                equipmentEthernetDTO.setData(equipmentEthernetDataList);
            }

            if (infoType.equals(ethernet))
                return equipmentEthernetDTO;
            else
                equipmentDetailData.setEthernet(equipmentEthernetDataList);
        }

        if (infoType.equals(all)) {
            equipmentDetailsDTO.setData(equipmentDetailData);
            return equipmentDetailsDTO;
        }

        if (infoType.equals(wireLess24g)) {
            EquipmentWirelessDTO equipmentWirelessDTO = new EquipmentWirelessDTO();
            EquipmentWirelessDTO.EquipmentWirelessData equipmentWirelessData = equipmentWirelessDTO.new EquipmentWirelessData();

            DBObject wireLess24g = (DBObject) ((DBObject) aPDetails.get("wifiRadios")).get("0");

            if (Objects.nonNull(wireLess24g)) {
                equipmentWirelessData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : (Double.parseDouble(txRxBytes.get("24gRxBytes").toString())));
                equipmentWirelessData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : (Double.parseDouble(txRxBytes.get("24gTxBytes").toString())));
                equipmentWirelessData.setBand(Objects.isNull(wireLess24g.get("band")) ? null : wireLess24g.get("band").toString());
                equipmentWirelessData.setChannel(Objects.isNull(wireLess24g.get("channel")) ? 0 : Long.valueOf(wireLess24g.get("channel").toString()));
                equipmentWirelessData.setChannelWidth(Objects.isNull(wireLess24g.get("channelWidth")) ? 0 : Long.valueOf(wireLess24g.get("channelWidth").toString()));
                equipmentWirelessData.setEnable(Objects.isNull(wireLess24g.get("enable")) ? 0 : Long.valueOf(wireLess24g.get("enable").toString()));
                equipmentWirelessData.setIsBackhaul(Objects.isNull(wireLess24g.get("isBackhaul")) ? false : Boolean.valueOf(wireLess24g.get("isBackhaul").toString()));
                Object operatingStandardsObj = wireLess24g.get("operatingStandards");
                StringBuilder opStandardStrBuilder = new StringBuilder();
                if (operatingStandardsObj != null) {
                    if (operatingStandardsObj instanceof BasicDBList) { // XXX: for backward compatible
                        boolean first = true;
                        for (Object opStandard : (BasicDBList) operatingStandardsObj) {
                            if (first) {
                                first = false;
                            } else {
                                opStandardStrBuilder.append(",");
                            }
                            opStandardStrBuilder.append(opStandard);
                        }
                    } else {
                        opStandardStrBuilder.append(operatingStandardsObj.toString());
                    }
                }
                equipmentWirelessData.setOperatingStandards(opStandardStrBuilder.toString());
                equipmentWirelessData.setMode("802.11 " + opStandardStrBuilder.toString());
                equipmentWirelessData.setDeviceAssociated(Objects.isNull(wireLess24g.get("devicesAssociated")) ? 0 : Double.valueOf(wireLess24g.get("devicesAssociated").toString()).longValue());
            }

            equipmentWirelessDTO.setData(equipmentWirelessData);

            return equipmentWirelessDTO;
        }

        if (infoType.equals(wireLess5g)) {
            EquipmentWirelessDTO equipmentWirelessDTO = new EquipmentWirelessDTO();
            EquipmentWirelessDTO.EquipmentWirelessData equipmentWirelessData = equipmentWirelessDTO.new EquipmentWirelessData();

            DBObject wireLess5g = (DBObject) ((DBObject) aPDetails.get("wifiRadios")).get("1");

            String isp = aPDetails.get("isp").toString();
            String type = aPDetails.get("type").toString();
            boolean isBackhaul = false;


            if(Objects.isNull(wireLess5g.get("isBackhaul"))) {
                if(isp.equals("windstream") && type.equals("EXTENDER")) {
                    isBackhaul = true;
                }
            } else {
                isBackhaul = Boolean.valueOf(wireLess5g.get("isBackhaul").toString());
            }

            if (Objects.nonNull(wireLess5g)) {
                equipmentWirelessData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : (Double.parseDouble(txRxBytes.get("5gRxBytes").toString())));
                equipmentWirelessData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : (Double.parseDouble(txRxBytes.get("5gTxBytes").toString())));
                equipmentWirelessData.setBand(Objects.isNull(wireLess5g.get("band")) ? null : wireLess5g.get("band").toString());
                equipmentWirelessData.setChannel(Objects.isNull(wireLess5g.get("channel")) ? 0 : Long.valueOf(wireLess5g.get("channel").toString()));
                equipmentWirelessData.setChannelWidth(Objects.isNull(wireLess5g.get("channelWidth")) ? 0 : Long.valueOf(wireLess5g.get("channelWidth").toString()));
                equipmentWirelessData.setEnable(Objects.isNull(wireLess5g.get("enable")) ? 0 : Long.valueOf(wireLess5g.get("enable").toString()));
                equipmentWirelessData.setIsBackhaul(isBackhaul);
                Object operatingStandardsObj = wireLess5g.get("operatingStandards");
                StringBuilder opStandardStrBuilder = new StringBuilder();
                if (operatingStandardsObj != null) {
                    if (operatingStandardsObj instanceof BasicDBList) { // XXX: for backward compatible
                        boolean first = true;
                        for (Object opStandard : (BasicDBList) operatingStandardsObj) {
                            if (first) {
                                first = false;
                            } else {
                                opStandardStrBuilder.append(",");
                            }
                            opStandardStrBuilder.append(opStandard);
                        }
                    } else {
                        opStandardStrBuilder.append(operatingStandardsObj.toString());
                    }
                }
                equipmentWirelessData.setOperatingStandards(opStandardStrBuilder.toString());
                equipmentWirelessData.setMode("802.11 " + opStandardStrBuilder.toString());
                equipmentWirelessData.setDeviceAssociated(Objects.isNull(wireLess5g.get("devicesAssociated")) ? 0 : Double.valueOf(wireLess5g.get("devicesAssociated").toString()).longValue());
            }

            equipmentWirelessDTO.setData(equipmentWirelessData);
            return equipmentWirelessDTO;
        }

        return new ApiResponseDTO();
    }


    public ApiResponseDTO getSteeringDetailsForSubscriber(String equipmentIdOrSerialOrSTN, long minutes, String
            type) throws Exception {
        // Steering Details
        manageCommonService.isDurationValid(minutes);
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
        boolean flag = (Objects.nonNull(commonProps.get(ABS_DATABASE)) && commonProps.get(ABS_DATABASE).equalsIgnoreCase("MONGO")) ? true : false;
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        HashSet<String> serialNumbers;
        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject projection = new BasicDBObject();
        projection.put("macAddress", 1);
        projection.put("friendlyName", 1);
        projection.put("modelName", 1);
        projection.put("serialNumber", 1);
        projection.put("ssids", 1);
        projection.put("type", 1);

        List<BasicDBObject> equipmentList = mongoService.findList(query, null, AP_DETAIL, projection);
        serialNumbers = (HashSet<String>) equipmentList.stream().map(ap -> String.valueOf(ap.get("serialNumber"))).collect(Collectors.toSet());
        HashMap<String, Object> bssidDetails = new HashMap<>();

        HashSet<String> bssidSets = new HashSet<>();
        for (BasicDBObject equipment : equipmentList) {
            manageCommonService.processBSSIDDetails(bssidDetails, bssidSets, equipment);
        }

        StringJoiner bssIdCsv = new StringJoiner(COMMA);
        bssidSets.forEach(item -> {
            bssIdCsv.add(item);
        });

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        EquipmentSteeringDTO equipmentSteeringDTO = new EquipmentSteeringDTO();
        ArrayList<EquipmentSteeringDTO.EquipmentSteeringData> equipmentSteeringDataList = new ArrayList<>();

        EquipmentDiagnosticDTO equipmentDiagnosticDTO = new EquipmentDiagnosticDTO();
        ArrayList<EquipmentDiagnosticDTO.EquipmentDiagnosticData> equipmentDiagnosticDataList = new ArrayList<>();

        Set<String> deviceMacSet = new HashSet<>();
        Set<String> bssidsSet = new HashSet<>();
        HashMap<String, String> hostNameData;
        HashMap<String, String> friendlyNameData;
        StringJoiner serialNumberCsv;

        HashMap<String, Object> rangeParam = new HashMap<>();
        rangeParam.put("key", TIMESTAMP);
        rangeParam.put("operand", "gt");

        List<String> cassandraProjection = new ArrayList<>();

        List<String> macAddressList = manageCommonService.getMacAddressFromUserId(userEquipment.getRgwSerial());
        HashMap<String, List<String>> inparams = new HashMap<>();
        inparams.put("macAddress", macAddressList);

        switch (type) {
            case evAssociation:

                List<EvAssociation> association;
                if (flag) {
                    List<DBObject> associationFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.BSID, DEVICE_ASSOCIATION, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    association = manageCommonService.convertToAssocEvent(associationFromMongo);
                } else {
                    association = (List<EvAssociation>) cassandraRepository.read(EvAssociation.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }

                deviceMacSet.clear();
                association.forEach(i -> {
                    Object macAdd = i.getMacAddress();
                    if (Objects.nonNull(macAdd))
                        deviceMacSet.add(macAdd.toString());
                });
                hostNameData = manageCommonService.getHostNameForStation(deviceMacSet, userEquipment);
                friendlyNameData = manageCommonService.getFriendlyNameForStation(deviceMacSet, userEquipment);

                association.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && (i.getTimestamp() <= currentTimestamp)) {

                        HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getBssid()));

                        EquipmentSteeringDTO.EquipmentSteeringData equipmentSteeringData = equipmentSteeringDTO.new EquipmentSteeringData();
                        equipmentSteeringData.setEvent("Assoc");
                        equipmentSteeringData.setTime(Long.valueOf(Objects.isNull(i.getTimestamp()) ? "0" : String.valueOf(i.getTimestamp())));
                        equipmentSteeringData.setHostname(hostNameData.get(String.valueOf(i.getMacAddress())));
                        equipmentSteeringData.setFriendlyName(friendlyNameData.get(String.valueOf(i.getMacAddress())));
                        equipmentSteeringData.setVendor(manageCommonService.getVendorName(String.valueOf(i.getMacAddress())));
                        equipmentSteeringData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : String.valueOf(i.getMacAddress()));

                        equipmentSteeringData.setToBand(BandType.getBandByIndex(i.getBand()));
                        equipmentSteeringData.setToDevice(details == null ? null : details.get("friendlyName"));
                        equipmentSteeringData.setToRssi(null);

                        equipmentSteeringData.setFromBand(null);
                        equipmentSteeringData.setFromDevice(null);
                        equipmentSteeringData.setFromRssi(null);

                        equipmentSteeringData.setActualBand(null);
                        equipmentSteeringData.setActualDevice(null);
                        equipmentSteeringData.setActualRssi(null);

                        equipmentSteeringData.setLog(null);
                        equipmentSteeringData.setSteeringType(null);
                        equipmentSteeringData.setoPhyrate(null);
                        equipmentSteeringData.settPhyrate(null);
                        equipmentSteeringData.setoAirusage(null);
                        equipmentSteeringData.settAirusage(null);
                        equipmentSteeringData.setoFat(null);
                        equipmentSteeringData.setiFat(null);
                        equipmentSteeringData.settFat(null);
                        equipmentSteeringDataList.add(equipmentSteeringData);
                    }

                });
                Collections.sort(equipmentSteeringDataList);
                Collections.reverse(equipmentSteeringDataList);
                //equipmentSteeringDataList.stream().sorted(Comparator.comparing((EquipmentSteeringDTO.EquipmentSteeringData p) -> Long.valueOf(p.getTime())).reversed()).collect(Collectors.toList());
                equipmentSteeringDTO.setData(equipmentSteeringDataList);
                break;

            case evDisassociation:
                List<EvDisassociation> disAssociation;

                if (flag) {
                    List<DBObject> disAssociationFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.BSID, DEVICE_DISASSOCIATION, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    disAssociation = manageCommonService.convertToDisAssocEvent(disAssociationFromMongo);
                } else {
                    inparams.put("assocType", ASSOC_TYPE_LIST);
                    disAssociation = (List<EvDisassociation>) cassandraRepository.read(EvDisassociation.class, query, rangeParam, inparams, cassandraProjection, minutes);
                    inparams.remove("assocType");
                }

                disAssociation.forEach(i -> {
                    Object macAdd = i.getMacAddress();
                    if (Objects.nonNull(macAdd))
                        deviceMacSet.add(macAdd.toString());
                });

                hostNameData = manageCommonService.getHostNameForStation(deviceMacSet, userEquipment);
                friendlyNameData = manageCommonService.getFriendlyNameForStation(deviceMacSet, userEquipment);

                disAssociation.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && i.getTimestamp() <= currentTimestamp) {

                        HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getBssid()));

                        EquipmentSteeringDTO.EquipmentSteeringData equipmentSteeringData = equipmentSteeringDTO.new EquipmentSteeringData();
                        equipmentSteeringData.setEvent("Disassoc");
                        equipmentSteeringData.setTime(Long.valueOf(Objects.isNull(i.getTimestamp()) ? "0" : String.valueOf(i.getTimestamp())));
                        equipmentSteeringData.setHostname(hostNameData.get(String.valueOf(i.getMacAddress())));
                        equipmentSteeringData.setFriendlyName(friendlyNameData.get(String.valueOf(i.getMacAddress())));
                        equipmentSteeringData.setVendor(manageCommonService.getVendorName(String.valueOf(i.getMacAddress())));
                        equipmentSteeringData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacAddress()) ? "N/A" : i.getMacAddress());

                        equipmentSteeringData.setFromDevice(details == null ? null : details.get("friendlyName"));
                        equipmentSteeringData.setFromBand(BandType.getBandByIndex(i.getBand()));
                        equipmentSteeringData.setFromRssi(null);

                        equipmentSteeringData.setToDevice(null);
                        equipmentSteeringData.setToBand(null);
                        equipmentSteeringData.setToRssi(null);

                        equipmentSteeringData.setActualDevice(null);
                        equipmentSteeringData.setActualBand(null);
                        equipmentSteeringData.setActualRssi(null);
                        equipmentSteeringData.setLog(null);
                        equipmentSteeringData.setSteeringType(null);
                        equipmentSteeringData.setoPhyrate(null);
                        equipmentSteeringData.settPhyrate(null);
                        equipmentSteeringData.setoAirusage(null);
                        equipmentSteeringData.settAirusage(null);
                        equipmentSteeringData.setoFat(null);
                        equipmentSteeringData.setiFat(null);
                        equipmentSteeringData.settFat(null);
                        equipmentSteeringDataList.add(equipmentSteeringData);
                    }
                });
                Collections.sort(equipmentSteeringDataList);
                Collections.reverse(equipmentSteeringDataList);
                //equipmentSteeringDataList.stream().sorted(Comparator.comparing((EquipmentSteeringDTO.EquipmentSteeringData p) -> Long.valueOf(p.getTime())).reversed()).collect(Collectors.toList());
                equipmentSteeringDTO.setData(equipmentSteeringDataList);
                break;

            case steer:
                List<EvSteeringLogs> steer;
                if (flag) {
                    List<DBObject> steerFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.OBSS_BSSID, DEVICE_STEERING, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    steer = manageCommonService.convertToEVSteeringLog(steerFromMongo);
                } else {
                    steer = (List<EvSteeringLogs>) cassandraRepository.read(EvSteeringLogs.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }

                steer = steer.stream().filter(p -> (!p.getSteeringend().equals(EMPTY_STRING))).collect(Collectors.toList());
                deviceMacSet.clear();
                steer.forEach(i -> {
                    Object macAdd = i.getMacaddress();
                    if (Objects.nonNull(macAdd))
                        deviceMacSet.add(macAdd.toString());
                });
                hostNameData = manageCommonService.getHostNameForStation(deviceMacSet, userEquipment);
                friendlyNameData = manageCommonService.getFriendlyNameForStation(deviceMacSet, userEquipment);

                steer.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && i.getTimestamp() <= currentTimestamp) {

                        EquipmentSteeringDTO.EquipmentSteeringData equipmentSteeringData = equipmentSteeringDTO.new EquipmentSteeringData();
                        equipmentSteeringData.setEvent("Steer");
                        equipmentSteeringData.setTime(Long.parseLong(Objects.isNull(i.getTimestamp()) ? "0" : String.valueOf(i.getTimestamp())));
                        equipmentSteeringData.setHostname(hostNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setFriendlyName(friendlyNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setVendor(manageCommonService.getVendorName(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? null : i.getMacaddress());
                        if (Objects.isNull(i.getObssbssid())) {
                            equipmentSteeringData.setFromDevice(null);
                            equipmentSteeringData.setFromBand(null);
                        } else {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(i.getObssbssid());
                            equipmentSteeringData.setFromDevice(details == null ? null : details.get("friendlyName"));
                            equipmentSteeringData.setFromBand(details == null ? null : details.get("band"));
                        }

                        equipmentSteeringData.setFromRssi(String.valueOf(i.getObssrssi()) == null ? "N/A" : String.valueOf(i.getObssrssi()));

                        if (Objects.isNull(i.getIbssbssid())) {
                            equipmentSteeringData.setToDevice(null);
                            equipmentSteeringData.setToBand(null);
                        } else {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(i.getIbssbssid());
                            equipmentSteeringData.setToDevice(details == null ? null : details.get("friendlyName"));
                            equipmentSteeringData.setToBand(details == null ? null : details.get("band"));

                        }

                        equipmentSteeringData.setToRssi(String.valueOf(i.getIbssrssi()) == null ? "N/A" : String.valueOf(i.getIbssrssi()));

                        if (Objects.isNull(i.getTbssbssid())) {
                            equipmentSteeringData.setActualDevice(null);
                            equipmentSteeringData.setActualBand(null);
                        } else {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(i.getTbssbssid());
                            equipmentSteeringData.setActualDevice(details == null ? null : details.get("friendlyName"));
                            equipmentSteeringData.setActualBand(details == null ? null : details.get("band"));
                        }
                        equipmentSteeringData.setActualRssi(String.valueOf(i.getTbssrssi()) == null ? "N/A" : String.valueOf(i.getTbssrssi()));
                        equipmentSteeringData.setLog(null);
                        equipmentSteeringData.setSteeringType(i.getSteeringtype());
                        equipmentSteeringData.setSteeringEnd(i.getSteeringend());
                        equipmentSteeringData.setoPhyrate(String.valueOf(i.getOphyrate()) == null ? "" : String.valueOf(i.getOphyrate()));
                        equipmentSteeringData.settPhyrate(String.valueOf(i.getTphyrate()) == null ? "" : String.valueOf(i.getTphyrate()));
                        equipmentSteeringData.setoAirusage(String.valueOf(i.getOairusage()) == null ? "" : String.valueOf(i.getOairusage()));
                        equipmentSteeringData.settAirusage(String.valueOf(i.getTairusage()) == null ? "" : String.valueOf(i.getTairusage()));
                        equipmentSteeringData.setoFat(String.valueOf(i.getOfat()) == null ? "" : String.valueOf(i.getOfat()));
                        equipmentSteeringData.setiFat(String.valueOf(i.getIfat()) == null ? "" : String.valueOf(i.getIfat()));
                        equipmentSteeringData.settFat(String.valueOf(i.getTfat()) == null ? "" : String.valueOf(i.getTfat()));
                        equipmentSteeringData.setSteeringTime(Long.parseLong(Objects.isNull(i.getSteeringtime()) ? "0" : String.valueOf(i.getSteeringtime())));
                        equipmentSteeringData.setToChannel(Integer.parseInt(Objects.isNull(i.getIchannel()) ? "0" : String.valueOf(i.getIchannel())));
                        equipmentSteeringData.setFromChannel(Integer.parseInt(Objects.isNull(i.getOchannel()) ? "0" : String.valueOf(i.getOchannel())));
                        equipmentSteeringData.setActualChannel(Integer.parseInt(Objects.isNull(i.getTchannel()) ? "0" : String.valueOf(i.getTchannel())));

                        equipmentSteeringDataList.add(equipmentSteeringData);
                    }

                });
                Collections.sort(equipmentSteeringDataList);
                Collections.reverse(equipmentSteeringDataList);
                //equipmentSteeringDataList.stream().sorted(Comparator.comparing((EquipmentSteeringDTO.EquipmentSteeringData p) -> Long.valueOf(p.getTime())).reversed()).collect(Collectors.toList());
                equipmentSteeringDTO.setData(equipmentSteeringDataList);
                break;

            case roam:
                List<EvRoaming> roam;
                if (flag) {
                    List<DBObject> roamFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.OBSSID, DEVICE_ROAMING, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    roam = manageCommonService.convertToRoamEvent(roamFromMongo);
                } else {
                    roam = (List<EvRoaming>) cassandraRepository.read(EvRoaming.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }
                bssidsSet.clear();
                roam.forEach(i -> {
                    Object macAdd = i.getMacaddress();
                    if (Objects.nonNull(macAdd))
                        bssidsSet.add(macAdd.toString());
                });

                hostNameData = manageCommonService.getHostNameForStation(bssidsSet, userEquipment);
                friendlyNameData = manageCommonService.getFriendlyNameForStation(bssidsSet, userEquipment);

                roam.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && i.getTimestamp() <= currentTimestamp) {

                        EquipmentSteeringDTO.EquipmentSteeringData equipmentSteeringData = equipmentSteeringDTO.new EquipmentSteeringData();
                        equipmentSteeringData.setEvent("Roam");
                        equipmentSteeringData.setTime(Long.valueOf(Objects.isNull(i.getTimestamp()) ? "0" : String.valueOf(i.getTimestamp())));
                        equipmentSteeringData.setHostname(hostNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setFriendlyName(friendlyNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setVendor(manageCommonService.getVendorName(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? null : i.getMacaddress());
                        if (Objects.isNull(i.getObssid())) {
                            equipmentSteeringData.setFromDevice(null);
                        } else {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(i.getObssid());
                            equipmentSteeringData.setFromDevice(details == null ? null : details.get("friendlyName"));
                        }
                        equipmentSteeringData.setFromBand(Objects.isNull(i.getOband()) ? null : (String.valueOf(i.getOband()).equals("1") ? "5G" : "2.4G"));
                        equipmentSteeringData.setFromRssi(String.valueOf(i.getOrssi()) == null ? "N/A" : String.valueOf(i.getOrssi()));

                        equipmentSteeringData.setToDevice(null);
                        equipmentSteeringData.setToBand(null);
                        equipmentSteeringData.setToRssi(null);

                        if (Objects.isNull(String.valueOf(i.getNbssid()))) {
                            equipmentSteeringData.setActualDevice(null);
                        } else {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getNbssid()));
                            equipmentSteeringData.setActualDevice(details == null ? null : details.get("friendlyName"));
                        }
                        equipmentSteeringData.setActualBand(Objects.isNull(i.getNband()) ? null : (BandType.getBandByIndex(i.getNband())));

                        equipmentSteeringData.setActualRssi(String.valueOf(i.getNrssi()) == null ? "N/A" : String.valueOf(i.getNrssi()));
                        equipmentSteeringData.setLog(null);
                        equipmentSteeringData.setSteeringType(null);
                        equipmentSteeringData.setoPhyrate(null);
                        equipmentSteeringData.settPhyrate(null);
                        equipmentSteeringData.setoAirusage(null);
                        equipmentSteeringData.settAirusage(null);
                        equipmentSteeringData.setoFat(null);
                        equipmentSteeringData.setiFat(null);
                        equipmentSteeringData.settFat(null);
                        equipmentSteeringDataList.add(equipmentSteeringData);
                    }

                });
                Collections.sort(equipmentSteeringDataList);
                Collections.reverse(equipmentSteeringDataList);
                //equipmentSteeringDataList.stream().sorted(Comparator.comparing((EquipmentSteeringDTO.EquipmentSteeringData p) -> Long.valueOf(p.getTime())).reversed()).collect(Collectors.toList());
                equipmentSteeringDTO.setData(equipmentSteeringDataList);
                break;

            case eventLog:
                serialNumberCsv = new StringJoiner(COMMA);
                serialNumbers.forEach(item -> {
                    serialNumberCsv.add(item);
                });

                List<EvLogs> eventLog;

                if (flag) {
                    List<DBObject> eventLogFromMongo = mongoService.findListByTimestamp(query, serialNumberCsv.toString(), ActiontecConstants.SERIAL_NUMBER, DEVICE_EVENT_LOG, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    eventLog = manageCommonService.convertToEvLogs(eventLogFromMongo);
                } else {
                    eventLog = (List<EvLogs>) cassandraRepository.read(EvLogs.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }

                bssidsSet.clear();
                eventLog.forEach(i -> {
                    Object macAdd = i.getMacaddress();
                    if (Objects.nonNull(macAdd))
                        bssidsSet.add(macAdd.toString());
                });
                hostNameData = manageCommonService.getHostNameForStation(bssidsSet, userEquipment);
                friendlyNameData = manageCommonService.getFriendlyNameForStation(bssidsSet, userEquipment);

                eventLog.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && Long.valueOf(String.valueOf(i.getTimestamp())) <= currentTimestamp) {

                        EquipmentSteeringDTO.EquipmentSteeringData equipmentSteeringData = equipmentSteeringDTO.new EquipmentSteeringData();
                        equipmentSteeringData.setEvent("Info");
                        equipmentSteeringData.setTime(Long.valueOf(Objects.isNull(i.getTimestamp()) ? "0" : String.valueOf(i.getTimestamp())));
                        equipmentSteeringData.setHostname(hostNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setFriendlyName(friendlyNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setVendor(manageCommonService.getVendorName(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? null : i.getMacaddress());
                        equipmentSteeringData.setFromDevice(null);
                        equipmentSteeringData.setFromBand(null);
                        equipmentSteeringData.setFromRssi(null);
                        equipmentSteeringData.setToDevice(null);
                        equipmentSteeringData.setToBand(null);
                        equipmentSteeringData.setToRssi(null);
                        equipmentSteeringData.setActualBand(null);
                        equipmentSteeringData.setActualDevice(null);
                        equipmentSteeringData.setActualRssi(null);
                        equipmentSteeringData.setLog(Objects.isNull(i.getLog()) ? null : i.getLog());
                        equipmentSteeringData.setSteeringType(null);
                        equipmentSteeringData.setoPhyrate(null);
                        equipmentSteeringData.settPhyrate(null);
                        equipmentSteeringData.setoAirusage(null);
                        equipmentSteeringData.settAirusage(null);
                        equipmentSteeringData.setoFat(null);
                        equipmentSteeringData.setiFat(null);
                        equipmentSteeringData.settFat(null);
                        equipmentSteeringDataList.add(equipmentSteeringData);
                    }
                });

                Collections.sort(equipmentSteeringDataList);
                Collections.reverse(equipmentSteeringDataList);
                //equipmentSteeringDataList.stream().sorted(Comparator.comparing((EquipmentSteeringDTO.EquipmentSteeringData p) -> Long.valueOf(p.getTime())).reversed()).collect(Collectors.toList());
                equipmentSteeringDTO.setData(equipmentSteeringDataList);
                break;

            case staConnectState:
                List<EvStaConnectLogs> evStaConnectLogs;
                if (flag) {
                    List<DBObject> evStaConnectLogsFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.BSID, DEVICE_STATION_CONNECT_LOGS, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    evStaConnectLogs = manageCommonService.convertToEvStaConnectLogs(evStaConnectLogsFromMongo);
                } else {
                    evStaConnectLogs = (List<EvStaConnectLogs>) cassandraRepository.read(EvStaConnectLogs.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }

                evStaConnectLogs.forEach(i -> {
                    Object macAdd = i.getMacaddress();
                    if (Objects.nonNull(macAdd))
                        deviceMacSet.add(macAdd.toString());
                });

                hostNameData = manageCommonService.getHostNameForStation(deviceMacSet, userEquipment);
                friendlyNameData = manageCommonService.getFriendlyNameForStation(deviceMacSet, userEquipment);

                evStaConnectLogs.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getStatus()) && Objects.nonNull(i.getTimestamp()) && Long.valueOf(String.valueOf(i.getTimestamp())) <= currentTimestamp) {

                        HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getBssid()));

                        EquipmentSteeringDTO.EquipmentSteeringData equipmentSteeringData = equipmentSteeringDTO.new EquipmentSteeringData();
                        equipmentSteeringData.setEvent(String.valueOf(i.getStatus()));
                        equipmentSteeringData.setTime(Long.valueOf(Objects.isNull(i.getTimestamp()) ? "0" : String.valueOf(i.getTimestamp())));
                        equipmentSteeringData.setHostname(hostNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setFriendlyName(friendlyNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setVendor(manageCommonService.getVendorName(String.valueOf(i.getMacaddress())));
                        equipmentSteeringData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? "N/A" : i.getMacaddress());

                        if ("QUIT".equals(String.valueOf(i.getStatus()))) {
                            equipmentSteeringData.setEvent("Leave");
                            equipmentSteeringData.setFromDevice(details == null ? null : details.get("friendlyName"));
                            equipmentSteeringData.setFromBand(BandType.getBandByIndex(i.getBand()));
                            equipmentSteeringData.setFromRssi(null);

                            equipmentSteeringData.setToDevice(null);
                            equipmentSteeringData.setToBand(null);
                            equipmentSteeringData.setToRssi(null);
                        } else if ("JOIN".equals(String.valueOf(i.getStatus()))) {
                            equipmentSteeringData.setFromBand(null);
                            equipmentSteeringData.setFromDevice(null);
                            equipmentSteeringData.setFromRssi(null);

                            equipmentSteeringData.setToBand(BandType.getBandByIndex(i.getBand()));
                            equipmentSteeringData.setToDevice(details == null ? null : details.get("friendlyName"));
                            equipmentSteeringData.setToRssi(null);
                        } else {
                            equipmentSteeringData.setFromBand(null);
                            equipmentSteeringData.setFromDevice(null);
                            equipmentSteeringData.setFromRssi(null);

                            equipmentSteeringData.setToDevice(null);
                            equipmentSteeringData.setToBand(null);
                            equipmentSteeringData.setToRssi(null);
                        }

                        equipmentSteeringData.setActualDevice(null);
                        equipmentSteeringData.setActualBand(null);
                        equipmentSteeringData.setActualRssi(null);
                        equipmentSteeringData.setLog(null);
                        equipmentSteeringData.setSteeringType(null);
                        equipmentSteeringData.setoPhyrate(null);
                        equipmentSteeringData.settPhyrate(null);
                        equipmentSteeringData.setoAirusage(null);
                        equipmentSteeringData.settAirusage(null);
                        equipmentSteeringData.setoFat(null);
                        equipmentSteeringData.setiFat(null);
                        equipmentSteeringData.settFat(null);
                        equipmentSteeringDataList.add(equipmentSteeringData);
                    }
                });
                Collections.sort(equipmentSteeringDataList);
                Collections.reverse(equipmentSteeringDataList);
                //equipmentSteeringDataList.stream().sorted(Comparator.comparing((EquipmentSteeringDTO.EquipmentSteeringData p) -> Long.valueOf(p.getTime())).reversed()).collect(Collectors.toList());
                equipmentSteeringDTO.setData(equipmentSteeringDataList);
                break;
            case diagnostic:

                List<EvTargetApVictim> diagnosticLogs;
                diagnosticLogs = (List<EvTargetApVictim>) cassandraRepository.read(EvTargetApVictim.class, query, rangeParam, inparams, cassandraProjection, minutes);

                deviceMacSet.clear();
                diagnosticLogs.forEach(i -> {
                    Object macAdd = i.getMacaddress();
                    if (Objects.nonNull(macAdd))
                        deviceMacSet.add(macAdd.toString());
                });
                hostNameData = manageCommonService.getHostNameForStation(deviceMacSet, userEquipment);
                friendlyNameData = manageCommonService.getFriendlyNameForStation(deviceMacSet, userEquipment);

                long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                diagnosticLogs.forEach( i -> {
                    if (Objects.nonNull(i.getTimestamp()) && Long.valueOf(String.valueOf(i.getTimestamp())) <= currentTimestamp) {
                        HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getBssid()));

                        EquipmentDiagnosticDTO.EquipmentDiagnosticData equipmentDiagnosticData = equipmentDiagnosticDTO.new EquipmentDiagnosticData();

                        equipmentDiagnosticData.setTime(Long.valueOf(Objects.isNull(i.getTimestamp()) ? "0" : String.valueOf(i.getTimestamp())));
                        equipmentDiagnosticData.setEvent(String.valueOf(i.getEvent()));
                        equipmentDiagnosticData.setMacAddress(String.valueOf(i.getMacaddress()));
                        equipmentDiagnosticData.setHostname(hostNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentDiagnosticData.setFriendlyName(friendlyNameData.get(String.valueOf(i.getMacaddress())));
                        equipmentDiagnosticData.setDevice(details == null ? null : details.get("friendlyName"));
                        equipmentDiagnosticData.setBand(details == null ? null : details.get("band"));
                        equipmentDiagnosticData.setVendor(manageCommonService.getVendorName(String.valueOf(i.getMacaddress())));
                        equipmentDiagnosticData.setTrigger(Long.valueOf(i.getTrigger()));
                        equipmentDiagnosticData.setTriggerDescription(ABS_TRIGGER_STRING.get(Long.valueOf(i.getTrigger()).intValue()));
                        equipmentDiagnosticData.setReasonCode(Long.valueOf(i.getReasoncode()));

                        if (EV_TARGET_AP_SELECTION.equals(equipmentDiagnosticData.getEvent())) {
                            equipmentDiagnosticData.setReasonDescription(ABS_TARGET_REASON_STRING.get(Long.valueOf(i.getReasoncode()).intValue()));
                        } else {
                            equipmentDiagnosticData.setReasonDescription(ABS_VICTIM_REASON_STRING.get(Long.valueOf(i.getReasoncode()).intValue()));
                        }

                        equipmentDiagnosticDataList.add(equipmentDiagnosticData);
                    }
                });
                Collections.sort(equipmentDiagnosticDataList);
                Collections.reverse(equipmentSteeringDataList);
                equipmentDiagnosticDTO.setData(equipmentDiagnosticDataList);
                break;
        }

        if(type.equals(diagnostic)) {
            return equipmentDiagnosticDTO;
        }
        return equipmentSteeringDTO;
    }

    public ApiResponseDTO getAPDetailsGraph(String equipmentIdOrSerialOrSTN, String serialNumber, String
            typeOf, long minutes) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        String userId = manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        manageCommonService.checkAPSerialNumberBelongsToUser(serialNumber, userEquipment);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        if (typeOf.equals(busy5g) || typeOf.equals(busy24g)) {
            EquipmentCommonDTO equipmentCommonDTO = new EquipmentCommonDTO();
            ArrayList<EquipmentCommonDTO.EquipmentBusyData> equipmentBusyData = new ArrayList<>();

            /*HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", userId);
            queryParams.put("serialNumber", serialNumber);

            List<DBObject> radioOccInfo = mongoService.findListByTimestamp(queryParams, AP_WIFI_INSIGHTS_PER_MINUTE, TIMESTAMP, minutes, DESC, mongoFieldOptions);*/

            List<Map<String, Object>> radioOccInfo = generateTimeSeriesDataForUser(userEquipment, serialNumber, minutes);
            if (!radioOccInfo.isEmpty()) {
                for(Map<String, Object> radioInfo : radioOccInfo) {
                    Map<String, Object> radioElement = (Map<String, Object>) radioInfo.get("radio");
                    if (typeOf.equals(busy24g)) {
                        Map<String, Object> wifi2g = (Map<String, Object>) radioElement.get("2g");
                        EquipmentCommonDTO.EquipmentBusyData busyData = equipmentCommonDTO.new EquipmentBusyData();
                        busyData.setTimestamp(Long.valueOf(Objects.isNull(radioInfo.get("timestamp")) ? "0" : radioInfo.get("timestamp").toString()));
                        busyData.setValue(Double.valueOf(wifi2g.get("occupancy").toString()));
                        equipmentBusyData.add(busyData);
                    } else if (typeOf.equals(busy5g)) {
                        Map<String, Object> wifi5g = (Map<String, Object>) radioElement.get("5g");
                        EquipmentCommonDTO.EquipmentBusyData busyData = equipmentCommonDTO.new EquipmentBusyData();
                        busyData.setTimestamp(Long.valueOf(Objects.isNull(radioInfo.get("timestamp")) ? "0" : radioInfo.get("timestamp").toString()));
                        busyData.setValue(Double.valueOf(wifi5g.get("occupancy").toString()));
                        equipmentBusyData.add(busyData);
                    }
                }
            }
            if (!equipmentBusyData.isEmpty()) {
                equipmentBusyData.sort(Comparator.comparingLong(EquipmentCommonDTO.EquipmentBusyData::getTimestamp));
                equipmentCommonDTO.setData(equipmentBusyData);
            }

            return equipmentCommonDTO;
        }
        return new ApiResponseDTO();
    }

    private List<Map<String, Object>> getEquipmentTimeSeriesDataForUser(Equipment userEquipment, String serialNumber,
                                                                        long minutes) throws Exception {
        List<Map<String, Object>> actualData = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("hourlyData", 1);

        Calendar now = Calendar.getInstance();
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", serialNumber);
        params.put("dateHour", dateCriteria);

        List<DBObject> dbObjectList = mongoService.findList(AP_WIFI_INSIGHTS_PER_MINUTE, params, DATE, ASC, mongoFieldOptions);

        if (Objects.nonNull(dbObjectList) && !dbObjectList.isEmpty()) {
            List<Map<String, Object>> combinedData = new ArrayList<>();
            for (DBObject dbObject : dbObjectList) {
                if (Objects.nonNull(dbObject.get("hourlyData"))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get("hourlyData");
                    for (String key : dataByDay.keySet()) {
                        List<Map<String, Object>> slidingDataMap = (List<Map<String, Object>>) dataByDay.get(key);
                        if (!slidingDataMap.isEmpty()) {
                            combinedData.addAll(slidingDataMap);
                        }
                    }
                }
            }

            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            List<Map<String, Object>> dbData = (List<Map<String, Object>>) combinedData.stream().filter(element -> Objects.nonNull(element.get("timestamp")) && (Long.valueOf(element.get("timestamp").toString()) > currTimeStamp)).collect(Collectors.toList());
            if (Objects.nonNull(dbData) && !dbData.isEmpty()) {
                        /*Collections.sort(actualData, new Comparator<DBObject>() {
                            public int compare(DBObject one, DBObject two) {
                                if (Long.valueOf(one.get("timestamp").toString()) < Long.valueOf(two.get("timestamp").toString()))
                                    return -1;
                                else if (Long.valueOf(one.get("timestamp").toString()) == Long.valueOf(two.get("timestamp").toString()))
                                    return 0;
                                else
                                    return 1;
                            }
                        });
                        Collections.reverse(actualData);*/
                dbData.sort((q1, q2) -> Long.compare((Long) q1.get("timestamp"), (Long) q2.get("timestamp")));
                actualData.addAll(dbData);
            }

        }

        return actualData;
    }

    public ApiResponseDTO getAPDetailsGraphByDevice(String equipmentIdOrSerialOrSTN, String
            serialNumber, String typeOf, long minutes) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        manageCommonService.checkAPSerialNumberBelongsToUser(serialNumber, userEquipment);

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        EquipmentBusyByDeviceDTO equipmentBusyByDeviceDTO = new EquipmentBusyByDeviceDTO();
        ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> equipmentBusyByDeviceDataList = new ArrayList<>();

        List<Map<String, Object>> radioBusyByDevices = getEquipmentTimeSeriesDataForUser(userEquipment, serialNumber, minutes);

        if (!radioBusyByDevices.isEmpty()) {
            Map<String, List<Map<String, Object>>> devMap = new HashMap<>();
            for (Map<String, Object> item: radioBusyByDevices) {
                Long timestamp = (Long) item.get("timestamp");
                List<Map<String, Object>> devices = Objects.isNull(((Map<String, Object>) item.get("wifiSta")).get(typeOf.equals(busy24gByDevice)? "2g": "5g"))? new ArrayList<>() : (List<Map<String, Object>>) ((Map<String, Object>) item.get("wifiSta")).get(typeOf.equals(busy24gByDevice)? "2g": "5g");

                if (Objects.nonNull(devices) && !devices.isEmpty()) {
                    for (Map<String, Object> device : devices) {
                        String macAddress = (String) device.get("macAddress");
                        device.put("timestamp", timestamp);
                        device.put("radio", typeOf.equals(busy24gByDevice)? "2.4 GHz": "5 GHz");
                        List<Map<String, Object>> devList = devMap.get(macAddress);
                        if (Objects.nonNull(devList) && !devList.isEmpty()) {
                            devList.add(device);
                        } else {
                            devList = new ArrayList<>();
                            devMap.put(macAddress, devList);
                            devList.add(device);
                        }
                    }
                }
            }

            Map<String, Double> aggregatedPercentage = new HashMap<>();
            devMap.values().forEach(devList -> {
                Double percentagePerHour = devList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.isNull(p.get("airTimePercentage")) ? "0.0" : p.get("airTimePercentage").toString())).sum();
                if (!devList.isEmpty()) {
                    String macAddress = (String) devList.get(0).get("macAddress");
                    aggregatedPercentage.put(macAddress, percentagePerHour);
                }
            });

            Map<String, Double> top5Devices = aggregatedPercentage.entrySet().parallelStream().sorted(Map.Entry.comparingByValue(Comparator.reverseOrder())).limit(5)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue, LinkedMap::new));

            Set<String> deviceMacAddr = top5Devices.keySet();
            HashMap<String, String> deviceFriendlyName = manageCommonService.getFriendlyNameForStation(deviceMacAddr, userEquipment);

            top5Devices.forEach((k, v) -> {
                List<Map<String, Object>> devEntries = devMap.get(k);
                Set<String> device5GMacList = devEntries.stream().map(element -> element.get("macAddress").toString()).collect(Collectors.toSet());
                HashMap<String, String> device5GFriendlyNameList = manageCommonService.getFriendlyNameForStation(device5GMacList, userEquipment);
                for (Map<String, Object> device : devEntries) {
                    device.put("name", device5GFriendlyNameList.get(String.valueOf(device.get("macAddress"))));
                }

                EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData equipmentBusyByDeviceData = equipmentBusyByDeviceDTO.new EquipmentBusyByDeviceData();
                equipmentBusyByDeviceData.setName(deviceFriendlyName.get(k));
                ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice> busyByDevicesList = new ArrayList<>();
                devEntries.forEach(element -> {
                    EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice busyByDevice = equipmentBusyByDeviceData.new BusyByDevice();
                    busyByDevice.setAirTimePercentage(Double.valueOf(Objects.isNull(element.get("airTimePercentage")) ? "0.0" : element.get("airTimePercentage").toString()));
                    busyByDevice.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(element.get("macAddress")) ? null : element.get("macAddress").toString());
                    busyByDevice.setName(Objects.isNull(element.get("name")) ? null : element.get("name").toString());
                    busyByDevice.setRadio(Objects.isNull(element.get("radio")) ? null : element.get("radio").toString());
                    busyByDevice.setTimestamp(Long.valueOf(Objects.isNull(element.get("timestamp")) ? "0" : element.get("timestamp").toString()));

                    busyByDevicesList.add(busyByDevice);
                });

                if (busyByDevicesList.isEmpty()) {
                	EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice busyByDevice = equipmentBusyByDeviceData.new BusyByDevice();
                	busyByDevicesList.add(busyByDevice);
                }

				equipmentBusyByDeviceData.setData(busyByDevicesList);
				equipmentBusyByDeviceDataList.add(equipmentBusyByDeviceData);
            });

            if (equipmentBusyByDeviceDataList.isEmpty()) {
            	ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice> busyByDevicesList = new ArrayList<>();
            	EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData equipmentBusyByDeviceData = equipmentBusyByDeviceDTO.new EquipmentBusyByDeviceData();
            	EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice busyByDevice = equipmentBusyByDeviceData.new BusyByDevice();
            	busyByDevicesList.add(busyByDevice);
            	equipmentBusyByDeviceData.setData(busyByDevicesList);
            	equipmentBusyByDeviceDataList.add(equipmentBusyByDeviceData);
            	equipmentBusyByDeviceDTO.setData(equipmentBusyByDeviceDataList);
            }
			equipmentBusyByDeviceDTO.setData(equipmentBusyByDeviceDataList);

			return equipmentBusyByDeviceDTO;
        }

        return new ApiResponseDTO();
    }

    public void updateFriendlyNameForEquipment(String equipmentIdOrSerialOrSTN, EquipmentFriendlyNameRequest
            friendlyNameRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.checkSerialNumOfEquipment(friendlyNameRequest.getSerialNumber(), userEquipment);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(friendlyNameRequest.getSerialNumber(), userEquipment);
        BasicDBObject insertField = new BasicDBObject();
        insertField.put("friendlyName", friendlyNameRequest.getFriendlyName());
        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
        basicDBObjectUpdate.put("$set", insertField);
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", friendlyNameRequest.getSerialNumber());
        mongoService.findAndModify(params, AP_DETAIL, null, basicDBObjectUpdate);
    }

    private void sendRebootRPC30(String tid, String userId, String serial, Map vmqResponse) throws Exception {
        String rpcUri = RpcConstants.DIAG_ACTION_URI;
        Map<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("action", RpcConstants.ACTION_REBOOT);
        String payload = mapper.writeValueAsString(payloadMap);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer maxTries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
        cpeRpcService.sendRpc(tid, userId, serial, rpcUri, "POST", payload);

        Map<String, Object> result = null;
        for (int i = 0; i < maxTries; i++) {
            try {
                Thread.sleep(THREAD_TO_SLEEP);
            } catch (InterruptedException e) {
                LOG.error("waitingRpcResult interrupted. tid:[ " + tid + "]");
                throw e;
            }

            result = cpeRpcService.readRpcResult(tid);
            if (result != null) {
                break;
            }
        }

        if (result != null) {
            if (org.apache.commons.lang3.StringUtils.equals(String.valueOf(result.get("code")), "200")) {
                HashMap<String, Object> dataMap = new HashMap<>();
                dataMap.put("_id", tid);
                dataMap.put("userId", userId);
                dataMap.put("rpcType", "AP-Reboot");
                dataMap.put("result", EXECUTE_OK);
                dataMap.put("rpcVer", "3.0");
                dataMap.put("date", new Date());
                dataMap.put("vmqStatus", vmqResponse);
                mongoService.create(RPC_RESULT_INFO, dataMap);
            } else {
                LOG.error("serial: [{}] tid: [{}] response: [{}]", serial, tid, result.toString());

                HashMap<String, Object> dataMap = new HashMap<>();
                dataMap.put("_id", tid);
                dataMap.put("userId", userId);
                dataMap.put("rpcType", "AP-Reboot");
                dataMap.put("result", EXECUTE_FAIL.toUpperCase());
                dataMap.put("rpcVer", "3.0");
                dataMap.put("date", new Date());
                dataMap.put("vmqStatus", vmqResponse);
                mongoService.create(RPC_RESULT_INFO, dataMap);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request failed, RPC result :: " + result.get("code"));
            }
        } else {
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("_id", tid);
            dataMap.put("userId", userId);
            dataMap.put("rpcType", "AP-Reboot");
            dataMap.put("result", TIME_OUT);
            dataMap.put("rpcVer", "3.0");
            dataMap.put("date", new Date());
            dataMap.put("vmqStatus", vmqResponse);
            mongoService.create(RPC_RESULT_INFO, dataMap);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
        }

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", userId);
        queryParams.put("serialNumber", serial);

        DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, new BasicDBObject("_id", ApplicationConstants.ZERO));
        if (Objects.nonNull(aPDetails)) {
            long lastMetricReceived = Long.valueOf(String.valueOf(("v3".equalsIgnoreCase(String.valueOf(aPDetails.get("etlVersion"))) || Objects.isNull(aPDetails.get("sendingTime"))) ? aPDetails.get("timestamp") : aPDetails.get("sendingTime")));

            HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
            int equipmentOfflineTime = 7;
            try {
                equipmentOfflineTime = Integer.valueOf(commonProps.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
            } catch (Exception e) {

            }

            DBObject dataToUpdate = new BasicDBObject();
            BasicDBObject dataToSet = new BasicDBObject();
            dataToSet.put("connectionStatus", "offline");
            dataToSet.put("sendingTime", lastMetricReceived - ((equipmentOfflineTime + 10) * 60 * 1000L));
            dataToUpdate.put("$set", dataToSet);

            BasicDBObject query = new BasicDBObject();
            query.clear();
            query.put("userId", userId);
            query.put("serialNumber", serial);

            mongoService.update(query, dataToUpdate, false, false, AP_DETAIL);
        }
    }

    public String getStatusFromVmq(String serial) throws Exception {

        if(vmqApiKey == null || vmqApiKey.isEmpty()) {
            return null;
        }

        List<String> vmqUrlList = mqttService.getVmqUrls();

        HashMap<String, String> headerInfo = new HashMap<>();

        String urlTemplate = "http://%s@%s:8888/api/v1/session/show/--client_id=%s/--statename/--peer_host/--session_started_at/--user/--client_id/";

        String url = String.format(urlTemplate, vmqApiKey, vmqUrlList.get(0), serial);
        LOG.info("--DEBUG-- " + url + "\n serial: " + serial + "\nvmqAddress:" + vmqUrlList.get(0));

        String httpResponse = null;
        try {
            httpResponse = httpService.doGet(url, headerInfo, null);
            LOG.info("--DEBUG-- " + httpResponse);
        } catch (Exception ex) {
            LOG.error(ex);
            return null;
        }
        return httpResponse;
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.EQUIPMENT_REBOOT)
    public Map<String, String> invokeInternetRPCMethodsForEquipment(String equipmentIdOrSerialOrSTN, String serialNumber, String
            operation, String macAddr, String band,HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(serialNumber, userEquipment);
//        manageCommonService.isEquipmentOffline(serialNumber, userAP);

//        manageCommonService.checkEquipmentOffline(userEquipment.getRgwSerial(), serialNumber);

        AgentVersion agentVersion = equipmentUtils.getAgentVersion(userEquipment.getRgwSerial(), serialNumber);

        String vmqResponse = getStatusFromVmq(serialNumber);
        Map<String, Object> vmqRawResponse = new HashMap<>();
        if(vmqResponse != null) {
            vmqRawResponse = objectMapper.readValue(vmqResponse, HashMap.class);
        }

        String tid = CommonUtils.generateUUID();

        HashMap<String, String> publishParams = new HashMap<>();
        publishParams.put("-USER_ID-", userEquipment.getRgwSerial());
        publishParams.put("-S_ID-", serialNumber == null ? userEquipment.getRgwSerial() : serialNumber);
        publishParams.put("-ID-", String.valueOf(Calendar.getInstance().getTimeInMillis()));
        publishParams.put("-TID-", tid);

        HashMap<String, Object> data = new HashMap<>();
        switch (operation) {
            case AP_REBOOT:
                if(agentVersion.getMajorVersion() > 3) {
                    sendRebootRPC30(tid, userEquipment.getRgwSerial(), serialNumber, vmqRawResponse);
                } else {
                    data.put("_id", tid);
                    data.put("rpcType", "AP-Reboot");
                    data.put("userId", userEquipment.getRgwSerial());
                    data.put("serialNumber", serialNumber == null ? userEquipment.getRgwSerial() : serialNumber);
                    data.put("rpcVer", "2.0");
                    data.put("vmqStatus", vmqRawResponse);
                    data.put("timestamp", ZonedDateTime.now().toEpochSecond());
                    data.put("date", new Date());

                    mongoService.create(RPC_RESULT_INFO, data);
                    HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
                    Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
                    rpcUtilityService.publishToTopic(publishParams, MqttTemplate.REBOOT_AP_FOR_USER, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);
                    manageCommonService.processRPCResult(tid, max_Tries, THREAD_TO_SLEEP);

                    BasicDBObject query = new BasicDBObject();
                    query.put("_id", tid);
                    DBObject rpcResult = mongoService.findOne(RPC_RESULT_INFO, query);
                    if (Objects.nonNull(rpcResult) && RPC_RESULT.equals(rpcResult.get("result"))) {
                        HashMap<String, Object> queryParams = new HashMap<>();
                        queryParams.put("userId", userEquipment.getRgwSerial());
                        queryParams.put("serialNumber", userEquipment.getRgwSerial());

                        DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, new BasicDBObject("_id", ApplicationConstants.ZERO));
                        if (Objects.nonNull(aPDetails)) {
                            long lastMetricReceived = Long.valueOf(String.valueOf(("v3".equalsIgnoreCase(String.valueOf(aPDetails.get("etlVersion"))) || Objects.isNull(aPDetails.get("sendingTime"))) ? aPDetails.get("timestamp") : aPDetails.get("sendingTime")));


                            HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
                            int equipmentOfflineTime = 7;
                            try {
                                equipmentOfflineTime = Integer.valueOf(commonProps.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
                            } catch (Exception e) {

                            }

                            DBObject dataToUpdate = new BasicDBObject();
                            BasicDBObject dataToSet = new BasicDBObject();
                            dataToSet.put("connectionStatus", "offline");
                            dataToSet.put("sendingTime", lastMetricReceived - ((equipmentOfflineTime + 10) * 60 * 1000L));
                            dataToUpdate.put("$set", dataToSet);

                            query.clear();
                            query.put("userId", userEquipment.getRgwSerial());
                            query.put("serialNumber", userEquipment.getRgwSerial());
                            mongoService.update(query, dataToUpdate, false, false, AP_DETAIL);
                        }
                    }
                }

                break;
            default:
                LOG.error(":::: Operation INVALID ::::");
                throw new ApiException(ApiResponseCode.BAD_REQUEST);
        }

        Map<String, String> result = new HashMap<>();
        result.put("tid", tid);
        return result;
    }


    public void forgetEquipment(String equipmentIdOrSerialOrSTN, String serialNumber) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId))
//            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(serialNumber, userEquipment);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("serialNumber", serialNumber);
        DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.nonNull(aPDetails.get("type")) && GATEWAY.equals(String.valueOf(aPDetails.get("type")))) {
            queryParams.clear();
            queryParams.put("userId", userEquipment.getRgwSerial());
            String[] rgwCleanUp = ActiontecConstants.cleanupRGW.split(",");
            cleanUpData(queryParams, rgwCleanUp);

            HashMap<String, Object> query = new HashMap<>();
            query.put("userId", userEquipment.getId());
            dataAccessService.deleteUpdateNative(ClusterInfoSQL.DELETE_USER_AP_FROM_CLUSTER_BY_USER_ID, query);
        } else if (Objects.nonNull(aPDetails.get("type")) && EXTENDER.equals(String.valueOf(aPDetails.get("type")))) {
            String[] extenderCleanUp = ActiontecConstants.cleanupExtender.split(",");
            cleanUpData(queryParams, extenderCleanUp);

            HashSet<String> macList = manageCommonService.findAllStationBySerialNumberAndUserId(userEquipment.getRgwSerial(), serialNumber);
            if (!macList.isEmpty()) {
                final String serialNo = userEquipment.getRgwSerial();
                Runnable cleanupUserStationSlidingData = () -> {
                    try {
                        DBObject query = new BasicDBObject().append("userId", serialNo);
                        BasicDBObject remove = new BasicDBObject();
                        for (String mac : macList) {
                            remove.put("devMap." + mac, "");
                        }
                        DBObject update = new BasicDBObject().append("$unset", remove);
                        mongoService.update(query, update, false, false, USER_STATION_SLIDING_DATA);
                    } catch (Exception e) {
                        LOG.error("Error during update result into DB", e);
                    }
                };
                new Thread(cleanupUserStationSlidingData).start();
            }
        }
    }

    private void cleanUpData(HashMap<String, String> queryParams, String[] collectionList) {
        Runnable runnable = () -> {
            try {
                for (String collectionName : collectionList) {
                    mongoService.deleteOne(queryParams, collectionName);
                }
            } catch (Exception e) {
                LOG.error("Error during perform delete operation", e);
            }
        };
        new Thread(runnable).start();
    }

    //    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.EQUIPMENT_RESET_WIFI)
    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.EQUIPMENT_CHANNEL_OPTIMIZATION)
    public void processChannelRescanOrWIFIResetOptimization(String equipmentIdOrSerialOrSTN, String
            serialNumber, String band, String objectType, HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(serialNumber, userEquipment);
//        manageCommonService.isEquipmentOffline(serialNumber, userAP);


        String rpcUri = String.format(RpcConstants.RADIO_ACTION_URI, band.toUpperCase());
        Map<String, Object> payloadMap = new HashMap<>();
        if(objectType.equals("DoACS")) {
        payloadMap.put("action", RpcConstants.ACTION_DO_ACS);
        } else if(objectType.equals("ResetWiFi")) {
            payloadMap.put("action", RpcConstants.ACTION_WIFI_RESET);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Serial: %s %s don't support", userEquipment.getRgwSerial(), objectType));
        }
        String payload = objectMapper.writeValueAsString(payloadMap);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userEquipment.getRgwSerial(), serialNumber, rpcUri, "POST", payload, max_Tries, THREAD_TO_SLEEP);

        if (!String.valueOf(rpcResult.get("code")).equals("200")) {
            LOG.error("response: {}", rpcResult.toString());
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, String.format("serial: %s %s RPC Failed", userEquipment.getRgwSerial(), objectType));
        }
    }

    public HashMap<String, Object> getEquipmentsSuggestion(String key) {
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        // OC-5171: equipment might be extender, need to find correct rg userId and search in elastic search
        String userId = equipmentUtils.getEquipmentUserId(key);

        HashMap<String, Object> response = esService.searchSuggestion(userId.isEmpty() ? key : userId, EQUIPMENT_INDEX, CommonUtils.isSysAdmin() ? null : CommonUtils.getGroupIdOfLoggedInUser());
        if (Objects.nonNull(response) && response.size() != 0) {
            if ((response.get("highlight").equals("rgwMAC") || response.get("highlight").equals("extMAC")) && dataSecurityMapping.contains(DataSecurityType.macAddress.name())) {
                response = new HashMap<>();
                return response;
            }

            if (response.get("highlight").equals("globalAccountNo") && dataSecurityMapping.contains(DataSecurityType.globalAccount.name())) {
                response = new HashMap<>();
                return response;
            }

            if (response.get("highlight").equals("camsAccountNo") && dataSecurityMapping.contains(DataSecurityType.camsAccount.name())) {
                response = new HashMap<>();
                return response;
            }

            if (response.get("highlight").equals("phoneNo") && dataSecurityMapping.contains(DataSecurityType.phoneNumber.name())) {
                response = new HashMap<>();
                return response;
            }

            if (Objects.nonNull(response.get("data"))) {
                List<EquipmentSearchDTO> equipmentSearchDTOS = (List<EquipmentSearchDTO>) response.get("data");
                equipmentSearchDTOS.forEach(equipmentSearchDTO -> {
                    equipmentSearchDTO.setGlobalAccountNo(dataSecurityMapping.contains(DataSecurityType.globalAccount.name()) ? manageCommonService.encrypt() : equipmentSearchDTO.getGlobalAccountNo());
                    equipmentSearchDTO.setCamsAccountNo(dataSecurityMapping.contains(DataSecurityType.camsAccount.name()) ? manageCommonService.encrypt() : equipmentSearchDTO.getCamsAccountNo());
                    equipmentSearchDTO.setExtMAC(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : equipmentSearchDTO.getExtMAC());
                    equipmentSearchDTO.setRgwMAC(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : equipmentSearchDTO.getRgwMAC());
                    equipmentSearchDTO.setPhoneNo(dataSecurityMapping.contains(DataSecurityType.phoneNumber.name()) ? manageCommonService.encrypt() : equipmentSearchDTO.getPhoneNo());
                });
            }
        }
        return response;
    }

    public ApiResponseDTO getNeighborScan(String serialNumberOrSubscriberIdOrRGWMAC, long minutes) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberIdOrRGWMAC))
//            serialNumberOrSubscriberIdOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberIdOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberIdOrRGWMAC);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHourSeconds(minutes));

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("dateHour", dateCriteria);

        List<BasicDBObject> neighborList = mongoService.findList(NEIGHBOR_DETAIL, queryParams, TIMESTAMP, DESC);

        EquipmentNeighborScanDTO equipmentNeighborScanDTO = new EquipmentNeighborScanDTO();
        ArrayList<EquipmentNeighborScanDTO.EquipmentNeighborScanData> equipmentNeighborScanList = new ArrayList<>();

        if (Objects.nonNull(neighborList) && !neighborList.isEmpty()) {
            for (DBObject neighbor : neighborList) {
                long timestamp = Long.valueOf(Objects.isNull(neighbor.get("timestamp")) ? "0" : neighbor.get("timestamp").toString());
                List<DBObject> neighborDevices = (List<DBObject>) neighbor.get("neighbors");

                if (!neighborDevices.isEmpty()) {
                    neighborDevices.forEach(item -> {
                        EquipmentNeighborScanDTO.EquipmentNeighborScanData equipmentNeighborScanData = equipmentNeighborScanDTO.new EquipmentNeighborScanData();
                        equipmentNeighborScanData.setTimestamp(timestamp);
                        equipmentNeighborScanData.setSSID(Objects.isNull(item.get("ssid")) ? null : item.get("ssid").toString());
                        equipmentNeighborScanData.setBSSID(Objects.isNull(item.get("bssid")) ? null : item.get("bssid").toString());
                        equipmentNeighborScanData.setVendor(manageCommonService.getVendorName(equipmentNeighborScanData.getBSSID()));
                        equipmentNeighborScanData.setBand(Objects.isNull(item.get("RadioIdx")) ? null : item.get("RadioIdx").toString());
                        equipmentNeighborScanData.setChannel(Objects.isNull(item.get("channel")) ? null : Integer.valueOf(item.get("channel").toString()));
                        equipmentNeighborScanData.setBandwidth(Objects.isNull(item.get("bandwidth")) ? null : Integer.valueOf(item.get("bandwidth").toString()));
                        equipmentNeighborScanData.setSignalStrength(Objects.isNull(item.get("signalStrength")) ? null : Integer.valueOf(item.get("signalStrength").toString()));
                        equipmentNeighborScanData.setNoise(Objects.isNull(item.get("noise")) ? null : Integer.valueOf(item.get("noise").toString()));
                        equipmentNeighborScanData.setReporterSerialNumber(Objects.isNull(neighbor.get("serialNumber")) ? userEquipment.getRgwSerial() : neighbor.get("serialNumber").toString());

                        DBObject supportStandard = (DBObject) item.get("SupportedStandards");
                        if(Objects.nonNull(supportStandard)) {
                            List<String> operatingStandardList = new ArrayList<>();
                            String operatingStandard = null;
                            if(supportStandard.get("a").toString().equals("1") || supportStandard.get("a").toString().equals("true")) operatingStandardList.add("a");
                            if(supportStandard.get("b").toString().equals("1") || supportStandard.get("b").toString().equals("true")) operatingStandardList.add("b");
                            if(supportStandard.get("g").toString().equals("1") || supportStandard.get("g").toString().equals("true")) operatingStandardList.add("g");
                            if(supportStandard.get("n").toString().equals("1") || supportStandard.get("n").toString().equals("true")) operatingStandardList.add("n");
                            if(supportStandard.get("ac").toString().equals("1") || supportStandard.get("ac").toString().equals("true")) operatingStandardList.add("ac");
                            if(supportStandard.get("ax").toString().equals("1") || supportStandard.get("ax").toString().equals("true")) operatingStandardList.add("ax");

                            if(supportStandard.get("be") != null)
                                if(supportStandard.get("be").toString().equals("1") || supportStandard.get("be").toString().equals("true")) operatingStandardList.add("be");

                            if(!operatingStandardList.isEmpty()) {
                                operatingStandard = operatingStandardList.stream().collect(Collectors.joining(COMMA));
                            }
                            equipmentNeighborScanData.setOperatingStandard(operatingStandard);
                        }
                        equipmentNeighborScanList.add(equipmentNeighborScanData);
                    });
                }
            }
        }

        equipmentNeighborScanDTO.setData(equipmentNeighborScanList);
        return equipmentNeighborScanDTO;
    }

    private void writeNeighborResultToDB(String userId, String serial, Map<String, Object> neighborResult) throws Exception {
        ZonedDateTime ldtNow = ZonedDateTime.now();
        BasicDBList neighborList = new BasicDBList();

        List<Map<String, Object>> responses = (List<Map<String, Object>>) neighborResult.get("response");
        for(Map<String, Object> response : responses) {
            List<Map<String, Object>> datas = (List<Map<String, Object>>) response.get("data");
            for(Map<String, Object> data : datas) {
                List<Map<String, Object>> neighbors = (List<Map<String, Object>>) data.get("List");
                for(Map<String, Object> neighbor : neighbors) {
                    BasicDBObject n = new BasicDBObject();
                    n.put("ssid", String.valueOf(neighbor.get("ssid")));
                    n.put("bssid", String.valueOf(neighbor.get("bssid")));
                    n.put("mode", String.valueOf(neighbor.get("mode")));
                    n.put("channel", Integer.valueOf(neighbor.get("channel").toString()));
                    n.put("bandwidth", Integer.valueOf(neighbor.get("bandwidth").toString()));
                    n.put("DTIMPeriod", Integer.valueOf(neighbor.get("DTIMPeriod").toString()));
                    n.put("beaconPeriod", Integer.valueOf(neighbor.get("beaconPeriod").toString()));
                    n.put("signalStrength", Integer.valueOf(neighbor.get("signalStrength").toString()));
                    n.put("noise", Integer.valueOf(neighbor.get("noise").toString()));
                    n.put("RadioIdx", String.valueOf(neighbor.get("RadioIdx")));
                    n.put("SupportedStandards", neighbor.get("SupportedStandards"));
                    neighborList.add(n);
                }
            }
        }

        if(!neighborList.isEmpty()) {
            BasicDBObject neighborObj = new BasicDBObject();
            neighborObj.put("dateCreated", new Date());
            neighborObj.put("dateHour", ldtNow.truncatedTo(ChronoUnit.HOURS).toEpochSecond());
            neighborObj.put("timestamp", ldtNow.toInstant().toEpochMilli());
            neighborObj.put("userId", userId);
            neighborObj.put("serialNumber", serial);
            neighborObj.put("triggerBy", "Manually");
            neighborObj.put("neighbors", neighborList);

            mongoService.create(NEIGHBOR_DETAIL, neighborObj);
        }
    }

    public void performNeighborScan(NeighborScanRequestDTO neighborScanRequestDTO, String serialNumberOrSubscriberIdOrRGWMAC) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberIdOrRGWMAC))
//            serialNumberOrSubscriberIdOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberIdOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberIdOrRGWMAC);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("serialNumber", userEquipment.getRgwSerial());

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        String isp = String.valueOf(aPDetails.get("isp"));

        List<Object> requestList = new ArrayList<>();
        String band = neighborScanRequestDTO.getBand();
        if(band.equals("2.4G") || band.equals("All")) {
            Map<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("action", RpcConstants.ACTION_NEIGHBOR_SCAN);

            HashMap<String, Object> data = new HashMap<>();
            data.put("uri", String.format(RpcConstants.RADIO_ACTION_URI, "2.4G"));
            data.put("method", "POST");
            data.put("payload", payloadMap);

            requestList.add(data);
        }

        if(band.equals("5G") || band.equals("All")) {
            Map<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("action", RpcConstants.ACTION_NEIGHBOR_SCAN);

            HashMap<String, Object> data = new HashMap<>();
            data.put("uri", String.format(RpcConstants.RADIO_ACTION_URI, "5G"));
            data.put("method", "POST");
            data.put("payload", payloadMap);

            requestList.add(data);
        }

        if(requestList.isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Don't support band of " + band);
        }

        String request = objectMapper.writeValueAsString(requestList);
        String tid = CommonUtils.generateUUID();

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(LONG_RPC_POLL_COUNT));
        Map<String, Object> rpcResult = cpeRpcService.sendRpc31AndWaitResult(tid, userEquipment.getRgwSerial(), neighborScanRequestDTO.getSerialNumber(), isp, request, max_Tries, THREAD_TO_SLEEP);

        writeNeighborResultToDB(userEquipment.getRgwSerial(), neighborScanRequestDTO.getSerialNumber(), rpcResult);
    }

    public ApiResponseDTO getChannelSelection(String serialNumberOrSubscriberIdOrRGWMAC, String band, String serialNumber, long minutes) throws Exception {

//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberIdOrRGWMAC))
//            serialNumberOrSubscriberIdOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberIdOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberIdOrRGWMAC);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        EquipmentChannelSelectionDTO equipmentChannelSelectionDTO = new EquipmentChannelSelectionDTO();
        ArrayList<EquipmentChannelSelectionDTO.EquipmentChannelSelectionData> equipmentChannelSelectionList = new ArrayList<>();

        List<Map<String, Object>> combinedData = getEquipmentTimeSeriesDataForUser(userEquipment, serialNumber, minutes);
        if (Objects.nonNull(combinedData) && !combinedData.isEmpty()) {
            combinedData.forEach(item -> { 
            EquipmentChannelSelectionDTO.EquipmentChannelSelectionData equipmentChannelSelectionData = equipmentChannelSelectionDTO.new EquipmentChannelSelectionData();
            equipmentChannelSelectionData.setTimestamp(Objects.isNull(item.get("timestamp")) ? 0 : Long.valueOf(item.get("timestamp").toString()));
            Map<String, Object> wifiElement = (Map<String, Object>) ((Map<String, Object>) item.get("radio")).get(band.equals("5g")? "5g": "2g");
            equipmentChannelSelectionData.setChannel(Objects.isNull(wifiElement.get("channelsInUse")) ? 0 : Integer.valueOf(wifiElement.get("channelsInUse").toString()));
            equipmentChannelSelectionList.add(equipmentChannelSelectionData);
            });  
        }    

        if(!equipmentChannelSelectionList.isEmpty()) {
            equipmentChannelSelectionDTO.setData(equipmentChannelSelectionList);
        }    

        return equipmentChannelSelectionDTO;
    }

    public ApiResponseDTO getChannelChangeEvent(String serialNumberOrSubscriberIdOrRGWMAC, long minutes) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberIdOrRGWMAC))
//            serialNumberOrSubscriberIdOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberIdOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberIdOrRGWMAC);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<Map<String, Object>> combinedData = getEquipmentTimeSeriesDataForUser(userEquipment, userEquipment.getRgwSerial(), minutes);

        EquipmentChannelChangeEventDTO equipmentChannelChangeEventDTO = new EquipmentChannelChangeEventDTO();
        ArrayList<EquipmentChannelChangeEventDTO.EquipmentChannelChangeEventData> equipmentChannelChangeEventList = new ArrayList<>();

        long timeStamp = 0;
        int lastChannel2g = 0;
        int currChannel2g = 0;
        int lastChannel5g = 0;
        int currChannel5g = 0; 

        if (Objects.nonNull(combinedData) && !combinedData.isEmpty()) {
            Iterator<Map<String, Object>> iterator = combinedData.iterator();
            while (iterator.hasNext()) {
                Map<String, Object> item = iterator.next();
                timeStamp = Long.valueOf(item.get("timestamp").toString());
                EquipmentChannelChangeEventDTO.EquipmentChannelChangeEventData equipmentChannelChangeEventData2g = equipmentChannelChangeEventDTO.new EquipmentChannelChangeEventData();

                Map<String, Object> wifiElement = (Map<String, Object>) ((Map<String, Object>) item.get("radio")).get("2g");
                if (lastChannel2g != 0) { 
                    currChannel2g = Objects.isNull(wifiElement.get("channelsInUse")) ? 0 : Integer.valueOf(wifiElement.get("channelsInUse").toString());
                    if((currChannel2g != 0) && (currChannel2g != lastChannel2g)) {
                                EquipmentChannelChangeEventDTO.EquipmentChannelChangeEventData equipmentChannelChangeEventData = equipmentChannelChangeEventDTO.new EquipmentChannelChangeEventData();
                                equipmentChannelChangeEventData.setSerialNumber(userEquipment.getRgwSerial());
                                equipmentChannelChangeEventData.setBand("2.4G");
                                equipmentChannelChangeEventData.setFromChannel(lastChannel2g);
                                equipmentChannelChangeEventData.setToChannel(currChannel2g);
                                equipmentChannelChangeEventData.setTimestamp(timeStamp);
                                equipmentChannelChangeEventList.add(equipmentChannelChangeEventData);
                                lastChannel2g = currChannel2g;
                    }    
                }    
                else {
                    lastChannel2g = Objects.isNull(wifiElement.get("channelsInUse")) ? 0 : Integer.valueOf(wifiElement.get("channelsInUse").toString());
                }    

                wifiElement = (Map<String, Object>) ((Map<String, Object>) item.get("radio")).get("5g");
                EquipmentChannelChangeEventDTO.EquipmentChannelChangeEventData equipmentChannelChangeEventData5g = equipmentChannelChangeEventDTO.new EquipmentChannelChangeEventData();

                if (lastChannel5g != 0) { 
                    currChannel5g = Objects.isNull(wifiElement.get("channelsInUse")) ? 0 : Integer.valueOf(wifiElement.get("channelsInUse").toString());
                    if((currChannel5g != 0) && (currChannel5g != lastChannel5g)) {
                                EquipmentChannelChangeEventDTO.EquipmentChannelChangeEventData equipmentChannelChangeEventData = equipmentChannelChangeEventDTO.new EquipmentChannelChangeEventData();
                                equipmentChannelChangeEventData.setSerialNumber(userEquipment.getRgwSerial());
                                equipmentChannelChangeEventData.setBand("5G");
                                equipmentChannelChangeEventData.setFromChannel(lastChannel5g);
                                equipmentChannelChangeEventData.setToChannel(currChannel5g);
                                equipmentChannelChangeEventData.setTimestamp(timeStamp);
                                equipmentChannelChangeEventList.add(equipmentChannelChangeEventData);
                                lastChannel5g = currChannel5g;
                    }    
                }    
                else {
                    lastChannel5g = Objects.isNull(wifiElement.get("channelsInUse")) ? 0 : Integer.valueOf(wifiElement.get("channelsInUse").toString());
                }    
            }    
        }

        if(!equipmentChannelChangeEventList.isEmpty()) {
            equipmentChannelChangeEventList.sort(Comparator.comparing(EquipmentChannelChangeEventDTO.EquipmentChannelChangeEventData::getBand).reversed().thenComparing(EquipmentChannelChangeEventDTO.EquipmentChannelChangeEventData::getTimestamp));
            equipmentChannelChangeEventDTO.setData(equipmentChannelChangeEventList);
        }
        return equipmentChannelChangeEventDTO;
    }

    public ApiResponseDTO getChannelInterference(String serialNumberOrSubscriberIdOrRGWMAC, String band, long minutes) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberIdOrRGWMAC))
//            serialNumberOrSubscriberIdOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberIdOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberIdOrRGWMAC);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHourSeconds(minutes));

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("dateHour", dateCriteria);

        List<BasicDBObject> neighborList = mongoService.findList(NEIGHBOR_DETAIL, queryParams, TIMESTAMP, DESC);

        EquipmentChannelInterferenceDTO equipmentChannelInterferenceDTO = new EquipmentChannelInterferenceDTO();
        EquipmentSsidDTO equipmentSsidDTO = new EquipmentSsidDTO();
        ArrayList<EquipmentChannelInterferenceDTO.EquipmentChannelInterferenceData> equipmentChannelInterferenceList = new ArrayList<>();

        int last2g = 0;
        int last5g = 0;
        if (Objects.nonNull(neighborList) && !neighborList.isEmpty()) {
            for (DBObject neighbor : neighborList) {
                EquipmentChannelInterferenceDTO.EquipmentChannelInterferenceData channelInterferenceData_2g = equipmentChannelInterferenceDTO.new EquipmentChannelInterferenceData();
                EquipmentChannelInterferenceDTO.EquipmentChannelInterferenceData channelInterferenceData_5g = equipmentChannelInterferenceDTO.new EquipmentChannelInterferenceData();

                ArrayList<EquipmentSsidDTO.EquipmentSsidData> ssidList_2g = new ArrayList<>();
                ArrayList<EquipmentSsidDTO.EquipmentSsidData> ssidList_5g = new ArrayList<>();

                long timestamp = Long.valueOf(Objects.isNull(neighbor.get("timestamp")) ? "0" : neighbor.get("timestamp").toString());

                List<DBObject> neighborDevices = (List<DBObject>) neighbor.get("neighbors");
                if (!neighborDevices.isEmpty()) {
                    neighborDevices.forEach(item -> {
                        EquipmentSsidDTO.EquipmentSsidData equipmentSsidData = equipmentSsidDTO.new EquipmentSsidData();

                        equipmentSsidData.setSsid(Objects.isNull(item.get("ssid")) ? null : item.get("ssid").toString());
                        equipmentSsidData.setBssid(Objects.isNull(item.get("bssid")) ? null : item.get("bssid").toString());
                        equipmentSsidData.setChannel(Objects.isNull(item.get("channel")) ? null : Integer.valueOf(item.get("channel").toString()));
                        equipmentSsidData.setBandwidth(Objects.isNull(item.get("bandwidth")) ? null : Integer.valueOf(item.get("bandwidth").toString()));
                        equipmentSsidData.setSignalStrength(Objects.isNull(item.get("signalStrength")) ? null : Integer.valueOf(item.get("signalStrength").toString()));
                        equipmentSsidData.setReporterSerialNumber(userEquipment.getRgwSerial());

                        String radio = item.get("RadioIdx").toString();
                        if(radio.equals("2.4G")) {
                            ssidList_2g.add(equipmentSsidData);
                        } else if(radio.equals("5G")) {
                            ssidList_5g.add(equipmentSsidData);
                        }
                    });
                    if((band.equals("24g") || band.equals("both")) && (last2g == 0) && !ssidList_2g.isEmpty()) {
                        last2g = 1;
                        channelInterferenceData_2g.setTimestamp(timestamp);
                        channelInterferenceData_2g.setBand("2.4G");
                        channelInterferenceData_2g.setSsids(ssidList_2g);
                        equipmentChannelInterferenceList.add(channelInterferenceData_2g);
                    }
                    if((band.equals("5g") || band.equals("both")) && (last5g == 0) && !ssidList_5g.isEmpty()) {
                        last5g = 1;
                        channelInterferenceData_5g.setTimestamp(timestamp);
                        channelInterferenceData_5g.setBand("5G");
                        channelInterferenceData_5g.setSsids(ssidList_5g);
                        equipmentChannelInterferenceList.add(channelInterferenceData_5g);
                    }
                }
            }
        }

        if(!equipmentChannelInterferenceList.isEmpty()) {
            equipmentChannelInterferenceDTO.setData(equipmentChannelInterferenceList);
        }
        return equipmentChannelInterferenceDTO;
    }

    public Object getDataElement(String serialNumberOrSubscriberIdOrRGWMAC) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberIdOrRGWMAC))
//            serialNumberOrSubscriberIdOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberIdOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberIdOrRGWMAC);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        String serial = manageCommonService.getGatewaySerialByUserId(userEquipment.getRgwSerial()).orElseGet(()->userEquipment.getRgwSerial());
        HashMap<String, String> queryParam = new HashMap<>();
        queryParam.put("userId", userEquipment.getRgwSerial());
        queryParam.put("serialNumber", serial);
        DBObject apDetail = mongoService.findOne(AP_DETAIL, queryParam);
        String isp = "";
        if (Objects.isNull(apDetail)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Unable to fetch ISP for "+userEquipment.getRgwSerial() +" RGW.");
        }
        isp = (String) apDetail.get("isp");
        String tid = CommonUtils.generateUUID();

        List<Object> deRequest = new ArrayList<>();
        HashMap<String, Object> postRequest = new HashMap<>();
        postRequest.put("-URI-", "/cpe-api/de");
        postRequest.put("-METHOD-", "GET");
        postRequest.put("-PAYLOAD-", "\"\"");

        HashMap<String, Object> request = rpcUtilityService.registrationPutRPC(postRequest, MqttTemplate.SSM_RPC_REQUEST);
        if (Objects.nonNull(request))
            deRequest.add(request);

        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-USER_ID-", userEquipment.getRgwSerial());
        publishParam.put("-S_ID-", serial);
        publishParam.put("-ISP-", isp);
        publishParam.put("-REQUEST-", mapper.writeValueAsString(deRequest));

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("isTimeout", false);
        data.put("userId", userEquipment.getRgwSerial());
        data.put("dateCreated", new Date());
        data.put("uri", postRequest.get("-URI-"));
        data.put("isp", isp);
        mongoService.create(JSON_RPC_V3_INFO, data);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(WAN_SPEED_TEST_RPC_POLL_COUNT));

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.SMM_RPC_CALL, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processDeRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    public RadioResponseDTO getDataOfRadios(String equipmentIdOrSerialOrSTN, String radioId) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String serial = manageCommonService.getControllerSerialByUserId(equipment.getRgwSerial()).orElseGet(()->equipment.getRgwSerial());
        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", serial);
        publishParam.put("-RADIO_ID-", radioId);

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/radios/" + radioId);
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        RadioResponseDTO radioResponseDTO;
        try {
            mongoService.create(JSON_RPC_V3_INFO, data);
            rpcUtilityService.publishToTopic(publishParam, MqttTemplate.GET_DATA_FOR_RADIO, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);
            Object ob = manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
            radioResponseDTO = new ObjectMapper().convertValue(((List) ob).get(0), RadioResponseDTO.class);
        } catch (Exception e) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error in processing request => " + e.getMessage());
        }
        return radioResponseDTO;
    }


    public Object putDataOfRadios(String equipmentIdOrSerialOrSTN, String radioId, RadioRequestDTO radioRequestDTO) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        List<BasicDBObject> apDetailList = getApDetailOfRadio5GBand(equipment.getRgwSerial());

        if (Objects.isNull(apDetailList) || apDetailList.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The Equipment with 5G radio  band  does not exist ");

        Object responseOb = new ArrayList<>();
        try {

            for (BasicDBObject apDetail : apDetailList) {

                String userId = apDetail.getString("userId");
                String serialNumber = apDetail.getString("serialNumber");

                boolean requestDFSEnable = radioRequestDTO.isDfsEnable();
                boolean dFSEnable = false;

                List<BasicDBObject> wifiRadios = (List<BasicDBObject>) apDetail.get("wifiRadios");
                BasicDBObject wifiRadio = wifiRadios.stream().filter(radio -> radio.get("band").equals("5G")).findAny().orElse(null);
                if (Objects.nonNull(wifiRadio))
                    dFSEnable = wifiRadio.getBoolean("dfsEnable");

                if (requestDFSEnable != dFSEnable) {

                    String tid = CommonUtils.generateUUID();
                    HashMap<String, String> publishParam = new HashMap<>();
                    publishParam.put("-TID-", tid);

                    publishParam.put("-USER_ID-", userId);
                    publishParam.put("-S_ID-", serialNumber);
                    publishParam.put("-RADIO_ID-", radioId);
                    publishParam.put("-DFSENABLE-", String.valueOf(radioRequestDTO.isDfsEnable()));

                    HashMap<String, Object> data = new HashMap<>();
                    data.put("_id", tid);
                    data.put("uri", "/radios/" + radioId);
                    data.put("method", "POST");
                    data.put("isTimeout", false);
                    data.put("userId", userId);
                    data.put("dateCreated", new Date());

                    mongoService.create(JSON_RPC_V3_INFO, data);

                    rpcUtilityService.publishToTopic(publishParam, MqttTemplate.PUT_DATA_FOR_RADIO, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

                    responseOb = manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
                    BasicDBObject query = new BasicDBObject();
                    query.put("userId", userId);
                    query.put("serialNumber", serialNumber);

                    BasicDBObject update = new BasicDBObject();
                    List<BasicDBObject> updatedWifiRadiosList = new ArrayList<>();

                    Object responseFinalOb = responseOb;
                    wifiRadios.forEach(radioBand -> {
                        if (radioBand.getString("band").equals("5G")) {
                            BasicDBObject b = (BasicDBObject) ((List) responseFinalOb).get(0);
                            radioBand.put("dfsEnable", Boolean.valueOf(String.valueOf(b.get("DFSEnable"))) ? 1 : 0);
                        }
                        updatedWifiRadiosList.add(radioBand);
                    });
                    apDetail.put("wifiRadios", updatedWifiRadiosList);
                    update.put("$set", apDetail);
                    mongoService.update(query, update, false, true, "apDetail");
                }
            }

            LOG.info("response payload " + responseOb.toString());

        } catch (Exception e) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error in proceessing request => " + e.getMessage());
        }
        return responseOb;
    }


    public List getApDetailOfRadio5GBand(String rgwSerial) throws Exception {

        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", rgwSerial);
        params.put("serialNumber",rgwSerial);
        params.put("wifiRadios.band", "5G");
        return mongoService.findList("apDetail", params, "userId", 1);
    }


    public RadioDFSResponse getApDetailOfRadio5GBandDFS(String equipmentIdOrSerialOrSTN) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", equipment.getRgwSerial());
        params.put("wifiRadios.band", "5G");
        List<BasicDBObject> apDetailList = mongoService.findList("apDetail", params, "userId", 1);

        RadioDFSResponse radioDFSResponse = new RadioDFSResponse();

        for (BasicDBObject eq : apDetailList) {
            String userId = eq.getString("userId");
            String serialNumber = eq.getString("serialNumber");

            if (userId.equals(serialNumber)) {
                List<BasicDBObject> wifiRadios = (List<BasicDBObject>) eq.get("wifiRadios");
                for (BasicDBObject radio : wifiRadios) {
                    if (radio.getString("band").equals("2.4G")) {
                        if (radio.get("dfsEnable") != null) {
                            radioDFSResponse.set_24G(Integer.valueOf(String.valueOf(radio.get("dfsEnable"))));
                        } else {
                            radioDFSResponse.set_24G(0);
                        }
                    } else {
                        if (radio.get("dfsEnable") != null) {
                            radioDFSResponse.set_5G(Integer.valueOf(String.valueOf(radio.get("dfsEnable"))));
                        } else {
                            radioDFSResponse.set_5G(0);
                        }
                    }
                }
            }
        }


        return radioDFSResponse;
    }

    public Object uploadImageToS3(MultipartFile fileName, String modelNumber) {

        if (Objects.isNull(fileName.getOriginalFilename()) || fileName.getOriginalFilename().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File Name cannot be null or empty");
        }

        String logoUrl = null;
        String humanReadablePath = StringUtils.cleanPath(fileName.getOriginalFilename());

        try {

            HashMap<String, String> resp = s3Service.uploadFile(fileName, UPLOAD_PATH + "/" + modelNumber + "/" + humanReadablePath);
            logoUrl = resp.get("secure_url");
        } catch (Exception e) {
            LOG.error("Error in uploading file");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error while uploading logo");
        }

        return logoUrl;
    }
}
