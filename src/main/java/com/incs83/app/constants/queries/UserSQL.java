/**
 * <AUTHOR> <PERSON>uri
 * @Created 28-May-2017
 */
package com.incs83.app.constants.queries;

/**
 * <AUTHOR>
 */
public interface UserSQL {

    String GET_USER_BY_EMAIL = "from User where email = :email";
    String GET_USER_BY_ID = "select id, email, firstName, lastName,  alexaEmail, googleHomeEmail from user where id = :id";
    String DELETE_USER_ROLE_BY_ID = "delete from user_role where user_id =:userId";
    String DELETE_USER_ROLE_BY_COMPARTMENT_ID = "delete from user_role where source =:id";
    String DELETE_USER_COMPARTMENT_BY_COMPARTMENT_ID = "delete from user_compartment where compartment_id =:id";
    String COUNT_USER_BY_COMPARTMENT_ID = "select count(uc.User_id) from user_compartment uc WHERE compartment_id =:id";
    String UPDATE_USER_ROLE = "update user_role set role_id =:role_id where user_id =:user_id";
    String UPDATE_USER_COMPARTMENT = "update user_compartment set compartment_id =:compartment_id where user_id =:user_id";

    String COUNT_OF_GET_PAGINATED_USER = "select count(*) from User";

    String COUNT_OF_GET_PAGINATED_USER_BY_COMPARTMENT = "select count(*) from User u join u.compartment compartment where compartment in (:compartment) ";

    String GET_USER_BY_ALEXA_EMAIL = "from User where alexaEmail =:alexaEmail";
    String GET_USER_BY_GOOGLE_HOME_EMAIL = "from User where googleHomeEmail =:googleHomeEmail";

    String GET_USERS = "select u.firstName as firstName,u.lastName as lastName,u.email as email,c.name as groupName, r.name as roleName,u.id as id,false as isSubscriber, u.createdAt as createdAt, u.internalUser from user as u inner join user_compartment as uc on(u.id=uc.user_id) inner join user_role as ur on (u.id=ur.user_id) inner join role r on (r.id = ur.role_id) inner join compartment c on (c.id = uc.compartment_id) %s %s";
    String GET_COUNT_OF_SEARCH_USERS = "select COUNT(*) as total from user as u inner join user_compartment as uc on(u.id=uc.user_id) inner join user_role as ur on (u.id=ur.user_id) inner join role r on (r.id = ur.role_id) inner join compartment c on (c.id = uc.compartment_id) %s %s";
    String COUNT_TOTAL_USERS = "SELECT count(*) from user ###";
    String GET_USER_ROLE_BY_ROLE = "Select user_id from user_role where role_id =:roleId";

    String TICKET_USER_BY_USER = "from TicketUser where assignedBy=:user OR assignedTo=:user ";

    String GET_USER_BY_USER_IDS ="from User where id in (:userIds) ";

    String GET_ALL_EXTERNAL_USER ="select id from User where internalUser = 0 ";

    String DELETE_USER_COMPARTMENT_BY_USER_ID = "delete from user_compartment where User_id =:userId";
    String GET_USER_FOR_ELASTIC = "select u.firstName as firstName,u.lastName as lastName,u.email as email,c.name as groupName, r.name as roleName,u.id as id, false as isSubscriber, u.createdAt as createdAt,u.createdBy as createdBy, u.googleHomeEmail, u.alexaEmail, u.company,c.id as groupId, r.id as roleId, u.internalUser from user as u inner join user_compartment as uc on(u.id=uc.user_id) inner join user_role as ur on (u.id=ur.user_id) inner join role r on (r.id = ur.role_id) inner join compartment c on (c.id = uc.compartment_id) %s %s";
}
