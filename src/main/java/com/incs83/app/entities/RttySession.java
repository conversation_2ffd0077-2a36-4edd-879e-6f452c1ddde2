package com.incs83.app.entities;

import com.actiontec.optim.platform.api.v6.enums.RttySessionStatus;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = "rtty_session")
public class RttySession {

    @Id
    @NotNull
    @Column(name = "id")
    private String id;

    @NotNull
    @Column(name = "equipment_id", unique = true)
    private String equipmentId;

    @NotNull
    @Column(name = "token")
    private String token;

    @NotNull
    @Column(name = "ssh_url")
    private String sshUrl;

    @NotNull
    @Column(name = "http_url")
    private String httpUrl;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private RttySessionStatus status;

    @NotNull
    @Column(name = "rtty_server_id")
    private String rttyServerId;

    @NotNull
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @NotNull
    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "expired_at")
    private LocalDateTime expiredAt;

    public RttySession() {
    }

    public RttySession(String id, String equipmentId, String token, String sshUrl, String httpUrl, RttySessionStatus status, String rttyServerId, LocalDateTime createdAt, String createdBy, LocalDateTime expiredAt) {
        this.id = id;
        this.equipmentId = equipmentId;
        this.token = token;
        this.sshUrl = sshUrl;
        this.httpUrl = httpUrl;
        this.status = status;
        this.rttyServerId = rttyServerId;
        this.createdAt = createdAt;
        this.createdBy = createdBy;
        this.expiredAt = expiredAt;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEquipmentId() {
        return equipmentId;
    }

    public void setEquipmentId(String equipmentId) {
        this.equipmentId = equipmentId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSshUrl() {
        return sshUrl;
    }

    public void setSshUrl(String sshUrl) {
        this.sshUrl = sshUrl;
    }

    public String getHttpUrl() {
        return httpUrl;
    }

    public void setHttpUrl(String httpUrl) {
        this.httpUrl = httpUrl;
    }

    public RttySessionStatus getStatus() {
        return status;
    }

    public void setStatus(RttySessionStatus status) {
        this.status = status;
    }

    public String getRttyServerId() {
        return rttyServerId;
    }

    public void setRttyServerId(String rttyServerId) {
        this.rttyServerId = rttyServerId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(LocalDateTime expiredAt) {
        this.expiredAt = expiredAt;
    }
}
