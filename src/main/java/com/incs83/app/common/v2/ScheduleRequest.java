package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class ScheduleRequest implements AuditableData {

    @NotBlank(message = "Schedule Name cannot be blank")
    @Size(min = 1, max = 255, message = "Schedule Name must be between 1 and 255 characters")
    @Pattern(regexp = "^[a-zA-Z\\d\\-_()\\s]+$", message = "schedule Name, Invalid characters Found")
    private String scheduleName;

    private String id;

    private String sampleSize;

    @NotBlank(message = "Task Type cannot be blank")
    @Size(min = 1, max = 255, message = "Task Type must be between 1 to 255 character")
    private String taskType;

    @Size(max = 255, message = "Input can be max 254 characters long")
    private String desc;

    private long scheduleTime;

    private boolean isRecurring;

    private boolean isRandom;

    @Pattern(regexp = "EXCEL|PDF", message = "validValues.ReportType")
    private String reportType;

    @NotBlank(message = "Cluster Id cannot be blank")
    private String clusterId;

    public boolean getIsRandom() {
        return isRandom;
    }

    public void setRandom(boolean random) {
        isRandom = random;
    }

    public String getScheduleName() {
        return scheduleName == null ? null : scheduleName.trim();
    }

    public void setScheduleName(String scheduleName) {
        this.scheduleName = scheduleName;
    }

    public String getSampleSize() {
        return sampleSize == null ? null : sampleSize.trim();
    }

    public void setSampleSize(String sampleSize) {
        this.sampleSize = sampleSize;
    }

    public String getTaskType() {
        return taskType == null ? null : taskType.trim();
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getDesc() {
        return desc == null ? null : desc.trim();
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public long getScheduleTime() {
        return scheduleTime;
    }

    public void setScheduleTime(long scheduleTime) {
        this.scheduleTime = scheduleTime;
    }

    public boolean getIsRecurring() {
        return isRecurring;
    }

    public void setIsRecurring(boolean isRecurring) {
        this.isRecurring = isRecurring;
    }

    public String getClusterId() {
        return clusterId == null ? null : clusterId.trim();
    }

    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    public boolean isRecurring() {
        return isRecurring;
    }

    public void setRecurring(boolean recurring) {
        isRecurring = recurring;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }
}





