package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.annotation.AuditLog;
import com.actiontec.optim.platform.api.v5.model.*;
import com.actiontec.optim.platform.service.RemoteAccessService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import static com.incs83.app.constants.misc.AuditorConstants.*;

@RestController
@RequestMapping(value = "/actiontec/api/v5/remoteAccess/equipment/{equipmentId}")
public class RemoteAccessController {

    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private AuditService auditService;

    @Autowired
    private RemoteAccessService remoteAccessService;

    @RequestMapping(value = "/http", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public RemoteAccessHttpResponse getHttpAccess(
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        RemoteAccessHttpResponse response = remoteAccessService.getHttpAccess(equipmentId);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, REMOTEACESS_HTTP_GET, null , "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "/http", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    public RemoteAccessResponse putHttpAccess(
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "request properties", required = true) @RequestBody RemoteAccessHttpRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        RemoteAccessResponse response = remoteAccessService.putHttpAccess(equipmentId, request);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, REMOTEACESS_HTTP_PUT, request, "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "/rtty", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public RemoteAccessRtty getRttyAccess(
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        RemoteAccessRtty response = remoteAccessService.getRttyAccess(equipmentId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, REMOTEACESS_RTTY_GET, null , "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "/rtty", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    public RemoteAccessRttyResponse putRttyAccess(
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "request properties", required = true) @RequestBody RemoteAccessRttyRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        RemoteAccessRttyResponse response = remoteAccessService.putRttyAccess(equipmentId, request);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, REMOTEACESS_RTTY_PUT, request, "200", response, httpServletRequest);
        return response;
    }
}
