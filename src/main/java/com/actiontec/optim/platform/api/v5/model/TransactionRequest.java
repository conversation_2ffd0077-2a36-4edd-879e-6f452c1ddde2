package com.actiontec.optim.platform.api.v5.model;

import java.util.List;

public class TransactionRequest extends ActionRequest{
    private PayLoad payload;

    public PayLoad getPayload() {
        return payload;
    }

    public void setPayload(PayLoad payload) {
        this.payload = payload;
    }

    public static class PayLoad{
        private String ispId;
        private String description;
        private String fileName;
        private List<String> serialNumbers;
        private int fileSize;

        public String getIspId() {
            return ispId;
        }

        public void setIspId(String ispId) {
            this.ispId = ispId;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public List<String> getSerialNumbers() {
            return serialNumbers;
        }

        public void setSerialNumbers(List<String> serialNumbers) {
            this.serialNumbers = serialNumbers;
        }

        public int getFileSize() {
            return fileSize;
        }

        public void setFileSize(int fileSize) {
            this.fileSize = fileSize;
        }
    }
}
