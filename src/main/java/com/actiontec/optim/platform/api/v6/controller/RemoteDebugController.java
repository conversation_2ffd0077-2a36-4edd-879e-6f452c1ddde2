package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.annotation.RemoteDebugOperation;
import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.service.RemoteDebugService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@Api(value = "(V6) Remote Debug", description = "API's for Remote Debug Operations", tags = {"Optim - (V6) Remote Debug"})
@RequestMapping(value = "/actiontec/api/v6/debug/sessions")
public class RemoteDebugController {

    @Autowired
    private RemoteDebugService remoteDebugService;

    @RemoteDebugOperation(operation = "Remote Debug Get All Sessions")
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<RttySessionDTO> getAllSessions(
            @ModelAttribute RttySessionQueryDTO queryDTO) throws Exception {

        return remoteDebugService.getAllSessions(queryDTO);
    }

    @RemoteDebugOperation(operation = "Remote Debug Create Session")
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public RttySessionInitResponse createSession(
            @ApiParam(value = "Create debug session properties", required = true) @RequestBody RttySessionInitRequest sessionRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return remoteDebugService.createSession(sessionRequest);
    }

    @RemoteDebugOperation(operation = "Remote Debug Join Or Extend Session")
    @RequestMapping(value = "/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public RttySessionActionResponse<?> joinOrExtendSession(
            @ApiParam(value = "Session ID", required = true) @PathVariable(name = "id") String sessionId,
            @ApiParam(value = "Join or extend session properties", required = true) @RequestBody RttySessionActionRequest joinRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return remoteDebugService.joinOrExtendSession(sessionId, joinRequest);
    }

    @RemoteDebugOperation(operation = "Remote Debug Delete Session")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteSession(
            @ApiParam(value = "Session ID", required = true) @PathVariable String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        remoteDebugService.deleteSession(id);
    }
}
