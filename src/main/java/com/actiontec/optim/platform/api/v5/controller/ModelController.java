package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.mapper.EquipmentModelResponseMapper;
import com.actiontec.optim.platform.api.v5.model.ModelRequest;
import com.actiontec.optim.platform.api.v5.model.ModelResponse;
import com.actiontec.optim.platform.model.EquipmentModel;
import com.actiontec.optim.platform.service.EquipmentModelService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/models")
public class ModelController {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private EquipmentModelService equipmentModelService;

    @Autowired
    private EquipmentModelResponseMapper equipmentModelResponseMapper;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<ModelResponse> getModels(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<EquipmentModel> equipmentModelList = equipmentModelService.findModels();
        List<ModelResponse> modelResponseList = equipmentModelResponseMapper.toModelResponseList(equipmentModelList);

        return modelResponseList;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> addModel(
            @ApiParam(value = "Model properties", required = true) @RequestBody ModelRequest modelRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        EquipmentModel equipmentModel = equipmentModelService.createModel(equipmentModelResponseMapper.toModel(modelRequest));
        if(equipmentModel == null) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Fail to create wrong model");
        }

        Map<String, String> modelResponse = new HashMap<>();
        modelResponse.put("id", equipmentModel.getId());
        modelResponse.put("fileEntry", equipmentModel.getSnapshot().getFileEntry());
        return modelResponse;
    }

    @RequestMapping(value = "/{modelId}", method = RequestMethod.GET, produces = "application/json")
    public List<ModelResponse> getModel(
            @ApiParam(value = "modelId", required = true) @PathVariable(name = "modelId") String modelId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<ModelResponse> modelResponseList = new ArrayList<>();
        EquipmentModel equipmentModel = equipmentModelService.findModelById(modelId);
        if(equipmentModel != null) {
            ModelResponse modelResponse = equipmentModelResponseMapper.toModelResponse(equipmentModel);
            modelResponseList.add(modelResponse);
        }

        return modelResponseList;
    }

    @RequestMapping(value = "/{modelId}", method = RequestMethod.PUT, produces = "application/json")
    public void updateModel(
            @ApiParam(value = "modelId", required = true) @PathVariable(name = "modelId") String modelId,
            @ApiParam(value = "Model properties", required = true) @RequestBody ModelRequest modelRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        Boolean ret = equipmentModelService.updateModelById(modelId, equipmentModelResponseMapper.toModel(modelRequest));
        if(!ret) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Fail to update model id:" + modelId);
        }
    }

    @RequestMapping(value = "/{modelId}", method = RequestMethod.DELETE, produces = "application/json")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteModel(
            @ApiParam(value = "modelId", required = true) @PathVariable(name = "modelId") String modelId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        Boolean ret = equipmentModelService.deleteModelById(modelId);
        if(!ret) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Fail to delete model id:" + modelId);
        }
    }
}
