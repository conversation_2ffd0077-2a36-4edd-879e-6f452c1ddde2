package com.actiontec.optim.platform.api.v5.model;

import java.util.ArrayList;

public class ModelRequest {

    private String name;
    private String equipmentType;
    private ArrayList<String> ispIds;
    private ModelSnapshotRequest snapshot;
    private ModelHardwareDto hardware;
    private ArrayList<String> features;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEquipmentType() {
        return equipmentType;
    }

    public void setEquipmentType(String equipmentType) {
        this.equipmentType = equipmentType;
    }

    public ArrayList<String> getIspIds() {
        return ispIds;
    }

    public void setIspIds(ArrayList<String> ispIds) {
        this.ispIds = ispIds;
    }

    public ModelSnapshotRequest getSnapshot() {
        return snapshot;
    }

    public void setSnapshot(ModelSnapshotRequest snapshot) {
        this.snapshot = snapshot;
    }

    public ModelHardwareDto getHardware() {
        return hardware;
    }

    public void setHardware(ModelHardwareDto hardware) {
        this.hardware = hardware;
    }

    public ArrayList<String> getFeatures() {
        return features;
    }

    public void setFeatures(ArrayList<String> features) {
        this.features = features;
    }
}
