package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.model.Ipv6Address;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class Ipv6AddressMapper {
    private static final Logger logger = LogManager.getLogger(Ipv6AddressMapper.class);

    public abstract Ipv6Address toIpv6Address(ApDetailDto.NetworkDto.InterfaceDto.Ipv6Dto.AddressDto addressDto);

    public abstract List<Ipv6Address> toIpv6Addresses(List<ApDetailDto.NetworkDto.InterfaceDto.Ipv6Dto.AddressDto> addressDtos);
}
