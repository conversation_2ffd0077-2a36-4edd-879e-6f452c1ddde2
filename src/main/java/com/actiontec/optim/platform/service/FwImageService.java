package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.FwVerDistributionDao;
import com.actiontec.optim.mongodb.dao.InternetServiceProviderDao;
import com.actiontec.optim.mongodb.dto.FwVerDistributionDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.constant.ActiontecSQL;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.mapper.FwImageMapper;
import com.actiontec.optim.platform.mapper.FwVerDistributionMapper;
import com.actiontec.optim.platform.model.EquipmentModel;
import com.actiontec.optim.platform.model.FwImage;
import com.actiontec.optim.platform.model.FwVerDistribution;
import com.actiontec.optim.platform.repository.EquipmentModelRepo;
import com.incs83.app.entities.Compartment;
import com.incs83.app.entities.Firmware;
import com.incs83.app.entities.OptimFile;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
public class FwImageService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    DataAccessService dataAccessService;

    @Autowired
    private FwImageMapper fwImageMapper;

    @Autowired
    private FwFileService fwFileService;

    @Autowired
    private AwsS3Service awsS3Service;

    @Autowired
    private FwVerDistributionDao fwVerDistributionDao;

    @Autowired
    private FwVerDistributionMapper fwVerDistributionMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private InternetServiceProviderDao internetServiceProviderDao;

    @Autowired
    private EquipmentModelRepo equipmentModelRepo;

    private boolean checkGroupExist(String groupId) {
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, groupId);
            return Objects.nonNull(compartment) && Objects.nonNull(compartment.getIspId());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return false;
    }

    private boolean checkEquipmentTypeExist(ArrayList<String> equipmentTypeIds) throws Exception {
        for (String equipmentTypeId : equipmentTypeIds) {
            EquipmentModel equipmentModel = equipmentModelRepo.findEquipmentModelByModelName(equipmentTypeId);
            if (equipmentModel == null) {
                return false;
            }
        }
        return true;
    }

    public Boolean createFwImage(FwImage fwImage, ArrayList<String> equipmentTypeIds) throws Exception {

        if (CommonUtils.isSysAdmin()) {
            boolean isGroupExist = checkGroupExist(fwImage.getGroupId());
            if (!isGroupExist) { return false; }
        } else {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            if(!fwImage.getGroupId().equals(groupId)) {
                return false;
            }
        }

        boolean equipmentTypeExist = checkEquipmentTypeExist(equipmentTypeIds);
        if (!equipmentTypeExist) { return false; }

        HashMap<String, String> param = new HashMap<>();
        param.put("groupId", fwImage.getGroupId());
        param.put("version", fwImage.getVersion());
        List<Firmware> firmwareList = (List<Firmware>) dataAccessService.read(Firmware.class, ActiontecSQL.GET_FIRMWARE_BY_GROUP_AND_VERSION, param);

        for (Firmware firmware : firmwareList) {
            for (String equipmentTypeId : equipmentTypeIds) {
                if (firmware.getEquipmentTypeIds().contains(equipmentTypeId)) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Firmware image already exists");
                }
            }
        }

        fwImage.setId(CommonUtils.generateUUID());
        Firmware firmware = fwImageMapper.toFirmware(fwImage);

        OptimFile optimFile = new OptimFile();
        optimFile.setId(CommonUtils.generateUUID());
        optimFile.setType(ApplicationConstants.OptimFileType.firmware.name());

        if(fwImage.getLocationType().equals(ApplicationConstants.TYPE_URI)) {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_SUCCESS);
        } else {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);
        }

        optimFile.setFileName(fwImage.getFileName());
        optimFile.setFileSize(fwImage.getFileSize());
        dataAccessService.create(OptimFile.class, optimFile);

        if( fwImage.getIsProduction() != null && fwImage.getIsProduction() == true) {
            param.clear();
            param.put("equipmentTypeIds", fwImage.getEquipmentTypeIds());
            dataAccessService.deleteUpdateNative(ActiontecSQL.UPDATE_FIRMWARE_PRODUCTION, param);
        }

        firmware.setOptimFile(optimFile);
        dataAccessService.create(Firmware.class, firmware);

        fwImage.setFileEntry("/actiontec/api/v5/files/" + firmware.getOptimFile().getId());

        return true;
    }

    private FwImage getFwImage(Firmware fw) {
        FwImage fwImage = fwImageMapper.toFwImage(fw);
        fwImage.setFileEntry("/actiontec/api/v5/files/" + fw.getOptimFile().getId());
        fwImage.setFileName(fw.getOptimFile().getFileName());
        fwImage.setFileSize(fw.getOptimFile().getFileSize());
        fwImage.setFileType(fw.getLocationType());
        fwImage.setFileStatus(fw.getOptimFile().getFileStatus());
        return fwImage;
    }

    private void addFwImage(List<String> grpIds, List<String> eqpIds, List<FwImage> fwImages, Firmware fw, String groupId) {

        Boolean isFound = true;

        if (grpIds != null) {
            for (String grpId : grpIds) {
                if (fw.getGroupId().equals(grpId)) {
                    fwImages.add(getFwImage(fw));
                    isFound = false;
                    break;
                }
            }
        }

        if (isFound && (eqpIds != null)) {
            for (String eqpId : eqpIds) {
                if (fw.getEquipmentTypeIds().contains(eqpId)) {
                    fwImages.add(getFwImage(fw));
                    break;
                }
            }
        }
    }

    public List<FwImage> findFwImagesByIds(String equipmentTypeIds, String groupIds) throws Exception {

        List<FwImage> fwImages = new ArrayList<>();
        List<String> grpIds = null;
        List<String> eqpIds = null;

        String groupId = CommonUtils.getGroupIdOfLoggedInUser();
        String allIspId = internetServiceProviderDao.getIspIdByName(ApplicationConstants.ALL_ISP);
        String allGroupId = at3Adapter.getGroupIdByIspId(allIspId);


        if(groupIds != null) {
            grpIds = new ArrayList<String>(Arrays.asList(groupIds.split(",")));
        }

        if(equipmentTypeIds != null) {
            eqpIds = new ArrayList<String>(Arrays.asList(equipmentTypeIds.split(",")));
        }

        List<Firmware> firmwares = (List<Firmware>) dataAccessService.read(Firmware.class);

        if(!firmwares.isEmpty()) {
            boolean isSysAdmin = CommonUtils.isSysAdmin();

            for(Firmware fw : firmwares) {
                // 先檢查權限：非系統管理員需要檢查群組權限
                if (!isSysAdmin && !fw.getGroupId().equals(groupId) && !fw.getGroupId().equals(allGroupId)) {
                    continue;
                }

                // 根據參數決定處理方式
                if(grpIds == null && eqpIds == null) {
                    fwImages.add(getFwImage(fw));
                } else {
                    addFwImage(grpIds, eqpIds, fwImages, fw, groupId);
                }
            }
        }

        return fwImages;
    }

    public FwImage findFwImageById(String imageId) throws Exception {

        FwImage fwImage = null;

        Firmware firmware = (Firmware) dataAccessService.read(Firmware.class, imageId);

        if(firmware != null) {
            fwImage = fwImageMapper.toFwImage(firmware);
            fwImage.setFileEntry("/actiontec/api/v5/files/" + firmware.getOptimFile().getId());
            fwImage.setFileName(firmware.getOptimFile().getFileName());
            fwImage.setFileSize(firmware.getOptimFile().getFileSize());
            fwImage.setFileType(firmware.getLocationType());
            fwImage.setFileStatus(firmware.getOptimFile().getFileStatus());
            fwImage.setSecureUrl(firmware.getOptimFile().getSecureUrl());
        }
        return fwImage;
    }

    public Boolean updateFwImageById(String imageId, FwImage fwImage, ArrayList<String> equipmentTypeIds) throws Exception {

        if (CommonUtils.isSysAdmin()) {
            boolean isGroupExist = checkGroupExist(fwImage.getGroupId());
            if (!isGroupExist) { return false; }
        } else {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            if(!fwImage.getGroupId().equals(groupId)) {
                return false;
            }
        }

        boolean equipmentTypeExist = checkEquipmentTypeExist(equipmentTypeIds);
        if (!equipmentTypeExist) { return false; }

        Firmware firmware = (Firmware) dataAccessService.read(Firmware.class, imageId);
        if(firmware == null) {
            return false;
        }

        firmware.setName((fwImage.getName() != null) ? fwImage.getName() : firmware.getName());
        firmware.setDescription((fwImage.getDescription() != null) ? fwImage.getDescription() : firmware.getDescription());
        firmware.setVersion((fwImage.getVersion() != null) ? fwImage.getVersion() : firmware.getVersion());
        firmware.setLocationType((fwImage.getLocationType() != null) ? fwImage.getLocationType() : firmware.getLocationType());
        firmware.getOptimFile().setFileName((fwImage.getFileName() != null) ? fwImage.getFileName() : firmware.getOptimFile().getFileName());
        firmware.getOptimFile().setFileSize(fwImage.getFileSize());
        firmware.setLocation((fwImage.getFileLocation() != null) ? fwImage.getFileLocation() : firmware.getLocation());
        firmware.setUsername((fwImage.getUsername() != null) ? fwImage.getUsername() : firmware.getUsername());
        firmware.setPassword((fwImage.getPassword() != null) ? fwImage.getPassword() : firmware.getPassword());
        firmware.setEquipmentTypeIds((fwImage.getEquipmentTypeIds() != null) ? fwImage.getEquipmentTypeIds() : firmware.getEquipmentTypeIds());
        firmware.setGroupId((fwImage.getGroupId() != null) ? fwImage.getGroupId() : firmware.getGroupId());
        firmware.setSeqVersion((fwImage.getSequenceVersion() != null) ? fwImage.getSequenceVersion() : firmware.getSeqVersion());
        firmware.setProduction((fwImage.getIsProduction() != null) ? fwImage.getIsProduction() : firmware.getProduction());

        if(firmware.getProduction() != null && firmware.getProduction() == true) {
            HashMap<String, String> param = new HashMap<>();
            param.put("equipmentTypeIds", fwImage.getEquipmentTypeIds());
            dataAccessService.deleteUpdateNative(ActiontecSQL.UPDATE_FIRMWARE_PRODUCTION, param);
        }

        dataAccessService.update(Firmware.class, firmware);
        return true;
    }

    public Boolean deleteFwImageById(String imageId) throws Exception {

        Firmware firmware = (Firmware) dataAccessService.read(Firmware.class, imageId);
        if(firmware == null) {
            return false;
        }

        if (!CommonUtils.isSysAdmin()) {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            if(!firmware.getGroupId().equals(groupId)) {
                return false;
            }
        }

        awsS3Service.deleteFile(firmware.getOptimFile());
        dataAccessService.delete(Firmware.class, imageId);
        return true;
    }

    public void updateFwImageStatus(String imageId, String status) throws Exception {

        Firmware firmware = (Firmware) dataAccessService.read(Firmware.class, imageId);
        firmware.getOptimFile().setFileStatus(status);
        dataAccessService.update(Firmware.class, firmware);

    }

    public List<FwVerDistribution> calculateFwVerDistribution() throws Exception {
        Optional<String> optionalIsp = Optional.empty();
        if (!CommonUtils.isSysAdmin()) {
            String ispId = at3Adapter.getIspId();
            String ispName = internetServiceProviderDao.getIspName(ispId);
            optionalIsp = Optional.of(ispName);
        }

        HashMap<String, String> props = commonService.read(com.incs83.constants.ApplicationCommonConstants.COMMON_CONFIG);
        String thresholdStr = props.get(com.incs83.constants.ApplicationCommonConstants.COMMON_DEVICE_STATUS_RETENTION_TIME);
        int threshold = Integer.parseInt(thresholdStr);
        List<FwVerDistributionDto> fwVerDistributionDtos = fwVerDistributionDao.calculateFwVerDistribution(optionalIsp, threshold);
        return fwVerDistributionMapper.toFwVerDistribution(fwVerDistributionDtos);
    }
}
