package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.exception.NoSuchModuleException;
import com.actiontec.optim.platform.model.NetworkVoice;
import com.actiontec.optim.platform.model.NetworkVoiceClient;
import com.actiontec.optim.platform.model.NetworkVoiceNet;
import com.actiontec.optim.platform.model.NetworkVoiceStats;
import com.actiontec.optim.platform.repository.NetworkVoiceRepo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NetworkVoiceService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private NetworkVoiceRepo networkVoiceRepo;

    public NetworkVoice findVoiceByStn(String stn) {
        String userId = at3Adapter.getRgwSerialByStn(stn);

        NetworkVoice networkVoice = networkVoiceRepo.findVoiceBySerial(userId);
        if(networkVoice == null) {
            throw new NoSuchModuleException();
        }

        return networkVoice;
    }

    public List<NetworkVoiceClient> findVoiceClientByStn(String stn) {
        String userId = at3Adapter.getRgwSerialByStn(stn);

        List<NetworkVoiceClient> networkVoiceClientList = networkVoiceRepo.findVoiceClientBySerial(userId);
        if(networkVoiceClientList == null) {
            throw new NoSuchModuleException();
        }

        return networkVoiceClientList;
    }

    public List<NetworkVoiceStats> findVoiceStatsByStnAndDuration(String stn, Long duration) {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        return networkVoiceRepo.findVoiceStatsBySerialAndDuration(userId, duration);
    }

    public List<NetworkVoiceClient> findVoiceClientByStnAndClientId(String stn, int clientId) {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        List<NetworkVoiceClient> networkVoiceClientList = networkVoiceRepo.findVoiceClientBySerialAndClientId(userId, clientId);
        if(networkVoiceClientList == null) {
            throw new NoSuchModuleException();
        }

        return networkVoiceClientList;
    }

    public NetworkVoiceNet findVoiceNetByStn(String stn) {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        NetworkVoiceNet networkVoiceNet = networkVoiceRepo.findVoiceNetBySerial(userId);
        if(networkVoiceNet == null) {
            throw new NoSuchModuleException();
        }

        return networkVoiceNet;
    }
}
