package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBObject;

public class OauthConfigExtraClientDataDto {
    public enum Fields {
        iosClientId,
        androidClientId
    }

    private String iosClientId;
    private String androidClientId;

    public static OauthConfigExtraClientDataDto fromBasicDbObject(BasicDBObject dbObj) {
        OauthConfigExtraClientDataDto oauthConfigExtraClientDataDto = new OauthConfigExtraClientDataDto();
        oauthConfigExtraClientDataDto.setIosClientId(dbObj.getString(Fields.iosClientId.name()));
        oauthConfigExtraClientDataDto.setAndroidClientId(dbObj.getString(Fields.androidClientId.name()));
        return oauthConfigExtraClientDataDto;
    }

    public String getIosClientId() {
        return iosClientId;
    }

    public void setIosClientId(String iosClientId) {
        this.iosClientId = iosClientId;
    }

    public String getAndroidClientId() {
        return androidClientId;
    }

    public void setAndroidClientId(String androidClientId) {
        this.androidClientId = androidClientId;
    }
}
