package com.actiontec.optim.util;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

public class CustomStringUtils extends StringUtils {
    public static final ObjectMapper objectMapper = new ObjectMapper();
    public static final String SPACE = " ";

    public static String toStringOrNull(Object obj) {
        return obj != null ? String.valueOf(obj) : null;
    }

    public static String toBatchEquipmentFileEntry(String fileId) {
        return fileId != null ? ApplicationConstants.PUBLIC_FILE_PATH + fileId : null;
    }
}
